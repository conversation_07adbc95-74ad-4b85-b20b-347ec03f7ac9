# 智能网页总结助手

一个基于AI的Chrome浏览器扩展程序，能够智能总结网页内容，支持自定义提示词模板和多种导出格式。

## ✨ 主要功能

### 🎯 核心功能
- **智能内容提取**: 自动识别并提取网页主要文本内容，过滤广告、导航等无关元素
- **AI智能总结**: 基于通义千问API生成高质量的内容摘要
- **Markdown提取**: 🆕 一键将网页内容转换为标准Markdown格式
- **侧边栏界面**: 现代化的侧边栏设计，不影响原网页浏览体验
- **一键操作**: 点击扩展图标即可快速总结或提取当前页面

### 🛠️ 高级功能
- **自定义提示词**: 支持创建和管理多种提示词模板
- **预设模板**: 内置新闻、学术、技术文档等专业模板
- **多格式导出**: 支持复制到剪贴板和导出为文本文件
- **Markdown编辑**: 🆕 实时预览、源码编辑、目录生成
- **智能分块**: 🆕 自动处理长文章，避免API限制
- **主题切换**: 支持深色/浅色主题切换
- **配置管理**: 完整的设置页面，支持配置导入导出

## 🚀 快速开始

### 安装要求
- Chrome 浏览器 88+ 或其他基于Chromium的浏览器
- 通义千问API密钥（从阿里云百炼平台获取）

### 安装步骤

1. **下载扩展程序**
   ```bash
   git clone https://github.com/your-repo/智能网页总结助手.git
   cd 智能网页总结助手
   ```

2. **加载到Chrome**
   - 打开Chrome浏览器
   - 访问 `chrome://extensions/`
   - 开启"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择项目文件夹

3. **配置API密钥**
   - 点击扩展图标打开侧边栏
   - 点击设置按钮进入配置页面
   - 在"API配置"标签页中输入您的通义千问API密钥
   - 点击"测试连接"验证配置
   - 保存配置

### 获取API密钥

1. 访问 [阿里云百炼平台](https://bailian.console.aliyun.com/)
2. 注册并登录账户
3. 创建应用并获取API密钥
4. 确保账户有足够的调用额度

## 📖 使用指南

### 基本使用

#### 网页总结
1. **打开扩展**: 点击浏览器工具栏中的扩展图标
2. **选择模板**: 在侧边栏中选择合适的总结模板
3. **开始总结**: 点击"总结当前页面"按钮
4. **查看结果**: 等待AI生成总结结果
5. **复制导出**: 使用复制或导出功能保存结果

#### Markdown提取 🆕
1. **打开扩展**: 点击浏览器工具栏中的扩展图标
2. **选择功能**: 点击"提取Markdown"按钮
3. **确认处理**: 对于长文章会显示确认对话框
4. **查看预览**: 在预览区域查看转换结果
5. **编辑保存**: 可切换到源码模式编辑，然后复制或下载

### 自定义提示词

1. 进入设置页面的"提示词模板"标签
2. 点击"添加模板"创建新模板
3. 输入模板名称、描述和提示词内容
4. 使用 `{content}` 作为网页内容的占位符
5. 保存模板并在侧边栏中使用

### 示例提示词模板

```
新闻总结模板：
请总结这篇新闻的主要内容，包括：
1. 核心事件
2. 关键人物
3. 时间地点
4. 影响和意义

内容：{content}
```

## ⚙️ 配置选项

### API配置
- **API提供商**: 通义千问（推荐）或OpenAI兼容
- **API密钥**: 您的服务密钥
- **API端点**: 服务器地址
- **模型选择**: qwen-plus、qwen-turbo等
- **参数调节**: 温度、最大输出长度等

### 界面设置
- **主题**: 深色/浅色主题
- **侧边栏宽度**: 300-500px可调
- **功能开关**: 字数统计、通知提醒等
- **自动总结**: 实验性功能

### 高级设置
- **数据管理**: 配置导入导出、历史清理
- **重置选项**: 恢复默认设置

## 🏗️ 技术架构

### 文件结构
```
├── manifest.json          # 扩展配置文件
├── background/            # Service Worker
│   └── background.js
├── sidebar/               # 侧边栏界面
│   ├── sidebar.html
│   ├── sidebar.css
│   └── sidebar.js
├── content/               # 内容脚本
│   └── content.js
├── options/               # 设置页面
│   ├── options.html
│   ├── options.css
│   └── options.js
├── utils/                 # 工具函数
│   ├── api.js
│   └── storage.js
├── libs/                  # 第三方库
│   └── markdown-parser.js
├── assets/                # 静态资源
│   └── icons/
└── docs/                  # 文档
    ├── qwen.md
    └── env.md
```

### 核心技术
- **Manifest V3**: 最新的Chrome扩展标准
- **Service Worker**: 后台处理逻辑
- **Content Scripts**: 网页内容提取
- **Chrome APIs**: 存储、标签页、侧边栏等
- **现代CSS**: 渐变、动画、响应式设计
- **ES6+**: 现代JavaScript语法

## 🔧 开发指南

### 本地开发

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd 智能网页总结助手
   ```

2. **修改代码**
   - 编辑相应的HTML、CSS、JS文件
   - 遵循项目的代码规范

3. **测试扩展**
   - 在Chrome中重新加载扩展
   - 测试各项功能是否正常

4. **调试技巧**
   - 使用Chrome开发者工具
   - 查看扩展程序页面的错误信息
   - 使用console.log进行调试

### 代码规范

- 使用ES6+语法
- 遵循语义化命名
- 添加适当的注释
- 保持代码整洁和可读性

## 🐛 故障排除

### 常见问题

**Q: 扩展无法加载？**
A: 检查manifest.json语法是否正确，确保所有文件路径存在。

**Q: API调用失败？**
A: 
- 检查API密钥是否正确
- 确认网络连接正常
- 验证API端点地址
- 检查账户余额

**Q: 内容提取不准确？**
A: 
- 某些网站可能有特殊的结构
- 可以尝试刷新页面后重试
- 检查网站是否使用了动态加载

**Q: 总结质量不佳？**
A: 
- 尝试使用不同的提示词模板
- 调整API参数（如温度值）
- 确保提取的内容质量良好

### 错误代码

- `401`: API密钥无效
- `403`: 访问被拒绝
- `429`: 调用频率超限
- `500`: 服务器错误

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

### 贡献流程
1. Fork项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

### 开发规范
- 遵循现有代码风格
- 添加必要的测试
- 更新相关文档
- 确保向后兼容

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [阿里云通义千问](https://tongyi.aliyun.com/) - 提供AI服务
- [Chrome Extensions API](https://developer.chrome.com/docs/extensions/) - 扩展开发框架
- 所有贡献者和用户的支持

## 📞 联系我们

- 项目主页: [GitHub Repository](https://github.com/your-repo)
- 问题反馈: [Issues](https://github.com/your-repo/issues)
- 邮箱: <EMAIL>

---

**智能网页总结助手** - 让AI帮您快速理解网页内容 🚀
