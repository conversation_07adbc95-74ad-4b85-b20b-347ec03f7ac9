# 钉钉文档闪存插件登录状态更新机制

## 核心触发机制：Cookie监听

插件使用Chrome的Cookie监听API检测用户登录状态变化：

```javascript
chrome.cookies.onChanged.addListener((changeInfo) => {
    const {removed, cookie} = changeInfo;
    const {name, domain} = cookie;
    
    if (domain === ".dingtalk.com" && name === "account") {
        if (!removed) {
            Hr.fetchOrgsAndConfigs();
        }
    }
});
```

## 状态更新流程

### 1. Cookie变化检测
- 监听钉钉域名（`.dingtalk.com`）下的`account` cookie
- 用户在钉钉登录页面完成认证后，cookie被设置或更新
- Cookie监听器立即捕获变化

### 2. 获取用户信息（fetchOrgsAndConfigs）
```javascript
async fetchOrgsAndConfigs() {
    try {
        // 获取用户所属组织
        const orgsResponse = await fetch(
            `${baseUrl}/portal/api/v1/mine/orgs?orgTypes=1,2,3`,
            { credentials: 'include' }
        );
        
        // 获取用户设置
        const settingsResponse = await fetch(
            `${baseUrl}/openapi/api/user/settings`,
            { credentials: 'include' }
        );
        
        // 更新本地状态
        this.setLogin(true);
        this.updateOrgs(orgsData);
        this.updateSettings(settingsData);
    } catch (error) {
        console.error('获取用户信息失败:', error);
    }
}
```

### 3. 状态管理和存储
使用Effector状态管理库：
```javascript
class AuthManager {
    constructor() {
        this.login = createStore(false);
        this.selectedCorpId = "";
        this.myOrgs = [];
    }
    
    setLogin(status) {
        this.login.change(status);
        chrome.storage.local.set({[LOGIN_KEY]: status});
    }
}
```

### 4. 全局通知机制
```javascript
async function notifyAllTabsAuthChange(isAuthenticated, traceId) {
    const tabs = await chrome.tabs.query({});
    
    for (const tab of tabs) {
        try {
            await chrome.tabs.sendMessage(tab.id, {
                key: 'CS_AUTH_STATUS_CHANGED',
                data: {
                    isAuthenticated: isAuthenticated,
                    traceId: traceId
                }
            });
        } catch (error) {
            // 忽略无法发送消息的标签页
        }
    }
}
```

## 关键存储键名
```javascript
const STORAGE_KEYS = {
    LOGIN_KEY: "Login",
    MY_ORGS_KEY: "MY_ORGS_KEY",
    SELECTED_CORP_ID: "selectedCorpId",
    USER_SETTINGS: "userSettings"
};
```

## 核心特点
1. **实时响应** - Cookie监听确保登录状态立即更新
2. **状态同步** - 内存状态与本地存储保持一致
3. **全局通知** - 所有标签页同步获得状态变化
4. **错误处理** - 失败时自动回滚状态
5. **功能激活** - 登录成功后自动创建右键菜单等功能