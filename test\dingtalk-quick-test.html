<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钉钉登录快速测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-card {
            background: white;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-card h2 {
            margin: 0 0 16px 0;
            color: #333;
        }
        
        .status-display {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 16px;
            margin: 16px 0;
            border-left: 4px solid #ddd;
        }
        
        .status-display.success {
            background: #f6ffed;
            border-left-color: #52c41a;
        }
        
        .status-display.error {
            background: #fff2f0;
            border-left-color: #ff4d4f;
        }
        
        .status-display.warning {
            background: #fffbe6;
            border-left-color: #faad14;
        }
        
        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 8px;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #40a9ff;
        }
        
        .btn.secondary {
            background: #f5f5f5;
            color: #333;
        }
        
        .btn.secondary:hover {
            background: #e6e6e6;
        }
        
        .step {
            display: flex;
            align-items: flex-start;
            margin-bottom: 16px;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        
        .step-number {
            background: #1890ff;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            margin-right: 12px;
            flex-shrink: 0;
        }
        
        .step-content {
            flex: 1;
        }
        
        .step-title {
            font-weight: 600;
            margin-bottom: 4px;
            color: #333;
        }
        
        .step-description {
            font-size: 14px;
            color: #666;
            line-height: 1.4;
        }
        
        .cookie-info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 4px;
            padding: 12px;
            margin: 12px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #1890ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="test-card">
        <h2>🔍 钉钉登录快速测试</h2>
        <p>这个页面可以帮助您快速检测钉钉登录状态和排查问题。</p>
        
        <div class="status-display" id="statusDisplay">
            <strong>当前状态：</strong><span id="statusText">等待检测...</span>
        </div>
        
        <button class="btn" onclick="checkAuthStatus()">
            <span id="checkBtnText">检查认证状态</span>
        </button>
        <button class="btn secondary" onclick="checkCookies()">检查Cookie</button>
        <button class="btn secondary" onclick="startLogin()">开始登录</button>
        <button class="btn secondary" onclick="clearResults()">清除结果</button>
    </div>
    
    <div class="test-card">
        <h2>📋 登录步骤指引</h2>
        
        <div class="step">
            <div class="step-number">1</div>
            <div class="step-content">
                <div class="step-title">点击"开始登录"</div>
                <div class="step-description">系统会打开钉钉文档登录页面</div>
            </div>
        </div>
        
        <div class="step">
            <div class="step-number">2</div>
            <div class="step-content">
                <div class="step-title">完成钉钉登录</div>
                <div class="step-description">在新页面中输入钉钉账号密码，完成登录验证</div>
            </div>
        </div>
        
        <div class="step">
            <div class="step-number">3</div>
            <div class="step-content">
                <div class="step-title">确认登录成功</div>
                <div class="step-description">登录成功后，页面会显示钉钉文档首页，顶部显示您的用户名</div>
            </div>
        </div>
        
        <div class="step">
            <div class="step-number">4</div>
            <div class="step-content">
                <div class="step-title">返回检查状态</div>
                <div class="step-description">返回此页面，点击"检查认证状态"，应该显示已登录状态</div>
            </div>
        </div>
    </div>
    
    <div class="test-card" id="resultsCard" style="display: none;">
        <h2>📊 检测结果</h2>
        <div id="resultsContent"></div>
    </div>

    <script>
        let isChecking = false;
        
        // 发送消息到background script
        function sendMessage(message) {
            return new Promise((resolve, reject) => {
                if (!chrome.runtime) {
                    reject(new Error('Chrome runtime不可用，请在扩展环境中运行'));
                    return;
                }
                
                chrome.runtime.sendMessage(message, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                    } else {
                        resolve(response);
                    }
                });
            });
        }
        
        // 更新状态显示
        function updateStatus(text, type = 'info') {
            const statusDisplay = document.getElementById('statusDisplay');
            const statusText = document.getElementById('statusText');
            
            statusText.textContent = text;
            statusDisplay.className = `status-display ${type}`;
        }
        
        // 显示结果
        function showResults(title, content) {
            const resultsCard = document.getElementById('resultsCard');
            const resultsContent = document.getElementById('resultsContent');
            
            resultsContent.innerHTML = `
                <h3>${title}</h3>
                <div class="cookie-info">${JSON.stringify(content, null, 2)}</div>
            `;
            
            resultsCard.style.display = 'block';
            resultsCard.scrollIntoView({ behavior: 'smooth' });
        }
        
        // 设置加载状态
        function setLoading(loading) {
            const checkBtn = document.getElementById('checkBtnText');
            if (loading) {
                checkBtn.innerHTML = '<span class="loading"></span>检测中...';
                isChecking = true;
            } else {
                checkBtn.textContent = '检查认证状态';
                isChecking = false;
            }
        }
        
        // 检查认证状态
        async function checkAuthStatus() {
            if (isChecking) return;
            
            setLoading(true);
            updateStatus('正在检查认证状态...', 'info');
            
            try {
                const response = await sendMessage({ action: 'getDingTalkAuthStatus' });
                
                if (response.success) {
                    const authData = response.data;
                    
                    if (authData.isAuthenticated) {
                        updateStatus(`✅ 已登录 - ${authData.userInfo?.name || '钉钉用户'}`, 'success');
                        
                        showResults('认证状态检查结果', {
                            status: '已认证',
                            user: authData.userInfo,
                            organizations: authData.organizations,
                            selectedOrg: authData.selectedOrganization
                        });
                    } else {
                        updateStatus('❌ 未登录 - 请先完成钉钉登录', 'error');
                        
                        showResults('认证状态检查结果', {
                            status: '未认证',
                            message: '请点击"开始登录"完成钉钉登录'
                        });
                    }
                } else {
                    updateStatus(`❌ 检查失败 - ${response.error}`, 'error');
                    showResults('认证状态检查失败', response);
                }
                
            } catch (error) {
                updateStatus(`❌ 检查异常 - ${error.message}`, 'error');
                showResults('认证状态检查异常', { error: error.message });
            } finally {
                setLoading(false);
            }
        }
        
        // 检查Cookie
        async function checkCookies() {
            updateStatus('正在检查Cookie...', 'info');
            
            try {
                const cookies = await chrome.cookies.getAll({
                    domain: '.dingtalk.com'
                });
                
                const authCookies = cookies.filter(cookie => 
                    ['account', 'login_aliyunid_ticket', '_tb_token_'].includes(cookie.name)
                );
                
                if (authCookies.length > 0) {
                    updateStatus(`✅ 找到 ${authCookies.length} 个认证Cookie`, 'success');
                    
                    showResults('Cookie检查结果', {
                        totalCookies: cookies.length,
                        authCookies: authCookies.map(c => ({
                            name: c.name,
                            domain: c.domain,
                            hasValue: !!c.value,
                            valueLength: c.value ? c.value.length : 0,
                            secure: c.secure,
                            httpOnly: c.httpOnly
                        }))
                    });
                } else {
                    updateStatus('❌ 未找到认证Cookie - 请先登录钉钉', 'error');
                    
                    showResults('Cookie检查结果', {
                        totalCookies: cookies.length,
                        authCookies: [],
                        message: '未找到钉钉认证Cookie，请先完成登录'
                    });
                }
                
            } catch (error) {
                updateStatus(`❌ Cookie检查失败 - ${error.message}`, 'error');
                showResults('Cookie检查失败', { error: error.message });
            }
        }
        
        // 开始登录
        async function startLogin() {
            updateStatus('正在打开登录页面...', 'info');
            
            try {
                const response = await sendMessage({ action: 'initiateDingTalkLogin' });
                
                if (response.success) {
                    updateStatus('✅ 登录页面已打开 - 请在新页面中完成登录', 'success');
                    
                    // 开始轮询检查
                    setTimeout(() => {
                        updateStatus('⏳ 等待登录完成... 完成后请点击"检查认证状态"', 'warning');
                        startPolling();
                    }, 3000);
                    
                } else {
                    updateStatus(`❌ 打开登录页面失败 - ${response.error}`, 'error');
                }
                
            } catch (error) {
                updateStatus(`❌ 登录操作失败 - ${error.message}`, 'error');
            }
        }
        
        // 开始轮询检查登录状态
        function startPolling() {
            let pollCount = 0;
            const maxPolls = 24; // 2分钟
            
            const pollInterval = setInterval(async () => {
                pollCount++;
                
                try {
                    const response = await sendMessage({ action: 'getDingTalkAuthStatus' });
                    
                    if (response.success && response.data.isAuthenticated) {
                        clearInterval(pollInterval);
                        updateStatus(`🎉 登录成功！欢迎 ${response.data.userInfo?.name}`, 'success');
                        
                        showResults('自动检测到登录成功', {
                            user: response.data.userInfo,
                            organizations: response.data.organizations
                        });
                        return;
                    }
                    
                    if (pollCount >= maxPolls) {
                        clearInterval(pollInterval);
                        updateStatus('⏰ 登录检测超时 - 请手动点击"检查认证状态"', 'warning');
                    }
                    
                } catch (error) {
                    console.error('轮询检查失败:', error);
                    if (pollCount >= maxPolls) {
                        clearInterval(pollInterval);
                    }
                }
            }, 5000);
        }
        
        // 清除结果
        function clearResults() {
            document.getElementById('resultsCard').style.display = 'none';
            updateStatus('等待检测...', 'info');
        }
        
        // 页面加载完成后自动检查一次
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(checkAuthStatus, 500);
        });
    </script>
</body>
</html>
