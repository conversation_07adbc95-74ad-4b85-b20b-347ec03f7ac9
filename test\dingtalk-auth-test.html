<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钉钉认证功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #333;
        }
        
        .test-button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 8px;
            margin-bottom: 8px;
        }
        
        .test-button:hover {
            background: #40a9ff;
        }
        
        .test-button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        
        .test-result {
            background: #f6f8fa;
            border: 1px solid #e1e4e8;
            border-radius: 4px;
            padding: 12px;
            margin-top: 12px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background: #52c41a; }
        .status-error { background: #ff4d4f; }
        .status-warning { background: #faad14; }
        .status-info { background: #1890ff; }
        
        .auth-info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 4px;
            padding: 12px;
            margin-top: 12px;
        }
        
        .auth-info h4 {
            margin: 0 0 8px 0;
            color: #0050b3;
        }
        
        .auth-info p {
            margin: 4px 0;
            color: #096dd9;
        }
    </style>
</head>
<body>
    <h1>钉钉认证功能测试</h1>
    
    <!-- 认证状态测试 -->
    <div class="test-container">
        <div class="test-title">认证状态检查</div>
        <button class="test-button" onclick="testAuthStatus()">获取认证状态</button>
        <button class="test-button" onclick="refreshAuthStatus()">刷新认证状态</button>
        <div id="authStatusResult" class="test-result" style="display: none;"></div>
        <div id="authInfo" class="auth-info" style="display: none;">
            <h4>当前认证信息</h4>
            <p><strong>登录状态:</strong> <span id="loginStatus">未知</span></p>
            <p><strong>用户名:</strong> <span id="userName">-</span></p>
            <p><strong>用户邮箱:</strong> <span id="userEmail">-</span></p>
            <p><strong>选中组织:</strong> <span id="selectedOrg">-</span></p>
            <p><strong>组织数量:</strong> <span id="orgCount">0</span></p>
        </div>
    </div>
    
    <!-- 登录测试 -->
    <div class="test-container">
        <div class="test-title">登录功能测试</div>
        <button class="test-button" onclick="testLogin()">发起登录</button>
        <button class="test-button" onclick="testLogout()">退出登录</button>
        <div id="loginResult" class="test-result" style="display: none;"></div>
    </div>
    
    <!-- 组织管理测试 -->
    <div class="test-container">
        <div class="test-title">组织管理测试</div>
        <select id="orgSelect" style="margin-right: 8px;">
            <option value="">请选择组织</option>
        </select>
        <button class="test-button" onclick="testOrgSelection()">切换组织</button>
        <div id="orgResult" class="test-result" style="display: none;"></div>
    </div>
    
    <!-- Cookie测试 -->
    <div class="test-container">
        <div class="test-title">Cookie状态测试</div>
        <button class="test-button" onclick="testCookieStatus()">检查Cookie</button>
        <div id="cookieResult" class="test-result" style="display: none;"></div>
    </div>
    
    <!-- 实时日志 -->
    <div class="test-container">
        <div class="test-title">实时日志</div>
        <button class="test-button" onclick="clearLog()">清空日志</button>
        <div id="logContainer" class="test-result">等待操作...</div>
    </div>

    <script>
        // 日志记录
        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const statusClass = `status-${type}`;
            
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `
                <span class="status-indicator ${statusClass}"></span>
                [${timestamp}] ${message}
            `;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('logContainer').innerHTML = '日志已清空';
        }
        
        // 发送消息到background script
        function sendMessage(message) {
            return new Promise((resolve, reject) => {
                if (!chrome.runtime) {
                    reject(new Error('Chrome runtime不可用'));
                    return;
                }
                
                chrome.runtime.sendMessage(message, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                    } else {
                        resolve(response);
                    }
                });
            });
        }
        
        // 显示结果
        function showResult(elementId, data, success = true) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.textContent = JSON.stringify(data, null, 2);
            element.style.color = success ? '#52c41a' : '#ff4d4f';
        }
        
        // 测试认证状态
        async function testAuthStatus() {
            log('开始获取认证状态...', 'info');
            
            try {
                const response = await sendMessage({ action: 'getDingTalkAuthStatus' });
                
                if (response.success) {
                    log('认证状态获取成功', 'success');
                    showResult('authStatusResult', response.data, true);
                    updateAuthInfo(response.data);
                } else {
                    log(`认证状态获取失败: ${response.error}`, 'error');
                    showResult('authStatusResult', response, false);
                }
                
            } catch (error) {
                log(`认证状态获取异常: ${error.message}`, 'error');
                showResult('authStatusResult', { error: error.message }, false);
            }
        }
        
        // 刷新认证状态
        async function refreshAuthStatus() {
            log('开始刷新认证状态...', 'info');
            
            try {
                const response = await sendMessage({ action: 'refreshDingTalkAuth' });
                
                if (response.success) {
                    log('认证状态刷新成功', 'success');
                    showResult('authStatusResult', response.data, true);
                    updateAuthInfo(response.data);
                } else {
                    log(`认证状态刷新失败: ${response.error}`, 'error');
                    showResult('authStatusResult', response, false);
                }
                
            } catch (error) {
                log(`认证状态刷新异常: ${error.message}`, 'error');
                showResult('authStatusResult', { error: error.message }, false);
            }
        }
        
        // 更新认证信息显示
        function updateAuthInfo(authData) {
            const authInfo = document.getElementById('authInfo');
            authInfo.style.display = 'block';
            
            document.getElementById('loginStatus').textContent = authData.isAuthenticated ? '已登录' : '未登录';
            document.getElementById('userName').textContent = authData.userInfo ? authData.userInfo.name : '-';
            document.getElementById('userEmail').textContent = authData.userInfo ? (authData.userInfo.email || authData.userInfo.userId) : '-';
            document.getElementById('selectedOrg').textContent = authData.selectedOrganization ? authData.selectedOrganization.corpName : '-';
            document.getElementById('orgCount').textContent = authData.organizations ? authData.organizations.length : 0;
            
            // 更新组织选择下拉框
            const orgSelect = document.getElementById('orgSelect');
            orgSelect.innerHTML = '<option value="">请选择组织</option>';
            
            if (authData.organizations) {
                authData.organizations.forEach(org => {
                    const option = document.createElement('option');
                    option.value = org.corpId;
                    option.textContent = org.corpName;
                    if (authData.selectedOrganization && authData.selectedOrganization.corpId === org.corpId) {
                        option.selected = true;
                    }
                    orgSelect.appendChild(option);
                });
            }
        }
        
        // 测试登录
        async function testLogin() {
            log('开始发起登录...', 'info');

            try {
                const response = await sendMessage({ action: 'initiateDingTalkLogin' });

                if (response.success) {
                    log('登录页面打开成功', 'success');
                    log('请按照以下步骤完成登录:', 'info');
                    log('1. 在新打开的页面中输入钉钉账号密码', 'info');
                    log('2. 完成登录后，该页面会显示钉钉文档首页', 'info');
                    log('3. 返回此测试页面，点击"获取认证状态"查看结果', 'info');
                    log('4. 如果状态未更新，请等待几秒后再次点击', 'info');

                    showResult('loginResult', {
                        ...response.data,
                        instructions: [
                            '1. 在新打开的页面中输入钉钉账号密码',
                            '2. 完成登录后，该页面会显示钉钉文档首页',
                            '3. 返回此测试页面，点击"获取认证状态"查看结果',
                            '4. 如果状态未更新，请等待几秒后再次点击'
                        ]
                    }, true);

                    // 开始轮询检查认证状态
                    startLoginPolling();

                } else {
                    log(`登录发起失败: ${response.error}`, 'error');
                    showResult('loginResult', response, false);
                }

            } catch (error) {
                log(`登录发起异常: ${error.message}`, 'error');
                showResult('loginResult', { error: error.message }, false);
            }
        }

        // 开始登录状态轮询
        function startLoginPolling() {
            let pollCount = 0;
            const maxPolls = 30; // 最多轮询30次（2.5分钟）

            const pollInterval = setInterval(async () => {
                pollCount++;

                try {
                    const response = await sendMessage({ action: 'getDingTalkAuthStatus' });

                    if (response.success && response.data.isAuthenticated) {
                        clearInterval(pollInterval);
                        log('检测到登录成功！', 'success');
                        updateAuthInfo(response.data);
                        return;
                    }

                    if (pollCount >= maxPolls) {
                        clearInterval(pollInterval);
                        log('登录检测超时，请手动点击"获取认证状态"检查', 'warning');
                    } else {
                        log(`正在检测登录状态... (${pollCount}/${maxPolls})`, 'info');
                    }

                } catch (error) {
                    console.error('轮询认证状态失败:', error);
                    if (pollCount >= maxPolls) {
                        clearInterval(pollInterval);
                    }
                }
            }, 5000); // 每5秒检查一次
        }
        
        // 测试登出
        async function testLogout() {
            log('开始退出登录...', 'info');
            
            try {
                const response = await sendMessage({ action: 'dingTalkLogout' });
                
                if (response.success) {
                    log('退出登录成功', 'success');
                    showResult('loginResult', response.data, true);
                    // 刷新认证状态
                    setTimeout(testAuthStatus, 1000);
                } else {
                    log(`退出登录失败: ${response.error}`, 'error');
                    showResult('loginResult', response, false);
                }
                
            } catch (error) {
                log(`退出登录异常: ${error.message}`, 'error');
                showResult('loginResult', { error: error.message }, false);
            }
        }
        
        // 测试组织选择
        async function testOrgSelection() {
            const orgSelect = document.getElementById('orgSelect');
            const selectedCorpId = orgSelect.value;
            
            if (!selectedCorpId) {
                log('请先选择一个组织', 'warning');
                return;
            }
            
            log(`开始切换到组织: ${orgSelect.options[orgSelect.selectedIndex].text}`, 'info');
            
            try {
                const response = await sendMessage({ 
                    action: 'selectDingTalkOrg', 
                    corpId: selectedCorpId 
                });
                
                if (response.success) {
                    log('组织切换成功', 'success');
                    showResult('orgResult', response.data, true);
                    // 刷新认证状态
                    setTimeout(testAuthStatus, 1000);
                } else {
                    log(`组织切换失败: ${response.error}`, 'error');
                    showResult('orgResult', response, false);
                }
                
            } catch (error) {
                log(`组织切换异常: ${error.message}`, 'error');
                showResult('orgResult', { error: error.message }, false);
            }
        }
        
        // 测试Cookie状态
        async function testCookieStatus() {
            log('开始检查Cookie状态...', 'info');
            
            try {
                // 检查钉钉域名下的Cookie
                const cookies = await chrome.cookies.getAll({
                    domain: '.dingtalk.com'
                });
                
                const accountCookies = cookies.filter(cookie => cookie.name === 'account');
                
                const cookieInfo = {
                    totalCookies: cookies.length,
                    accountCookies: accountCookies.length,
                    cookieDetails: accountCookies.map(cookie => ({
                        name: cookie.name,
                        domain: cookie.domain,
                        path: cookie.path,
                        secure: cookie.secure,
                        httpOnly: cookie.httpOnly,
                        expirationDate: cookie.expirationDate ? new Date(cookie.expirationDate * 1000).toISOString() : null,
                        hasValue: !!cookie.value
                    }))
                };
                
                log(`Cookie检查完成，找到 ${cookies.length} 个Cookie，其中 ${accountCookies.length} 个account Cookie`, 'success');
                showResult('cookieResult', cookieInfo, true);
                
            } catch (error) {
                log(`Cookie检查异常: ${error.message}`, 'error');
                showResult('cookieResult', { error: error.message }, false);
            }
        }
        
        // 页面加载完成后自动获取认证状态
        document.addEventListener('DOMContentLoaded', () => {
            log('钉钉认证测试页面已加载', 'info');
            setTimeout(testAuthStatus, 500);
        });
    </script>
</body>
</html>
