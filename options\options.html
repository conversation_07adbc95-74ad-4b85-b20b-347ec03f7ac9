<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能网页总结助手 - 设置</title>
    <link rel="stylesheet" href="options.css">
</head>
<body>
    <div class="settings-container">
        <!-- 头部 -->
        <header class="settings-header">
            <div class="header-content">
                <div class="logo-section">
                    <div class="logo-icon">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                            <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                            <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                            <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <div class="logo-text">
                        <h1>智能网页总结助手</h1>
                        <p>设置与配置</p>
                    </div>
                </div>
                <div class="header-actions">
                    <button class="icon-btn" id="themeToggle" title="切换主题">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <circle cx="12" cy="12" r="5" stroke="currentColor" stroke-width="2"/>
                            <line x1="12" y1="1" x2="12" y2="3" stroke="currentColor" stroke-width="2"/>
                            <line x1="12" y1="21" x2="12" y2="23" stroke="currentColor" stroke-width="2"/>
                            <line x1="4.22" y1="4.22" x2="5.64" y2="5.64" stroke="currentColor" stroke-width="2"/>
                            <line x1="18.36" y1="18.36" x2="19.78" y2="19.78" stroke="currentColor" stroke-width="2"/>
                            <line x1="1" y1="12" x2="3" y2="12" stroke="currentColor" stroke-width="2"/>
                            <line x1="21" y1="12" x2="23" y2="12" stroke="currentColor" stroke-width="2"/>
                            <line x1="4.22" y1="19.78" x2="5.64" y2="18.36" stroke="currentColor" stroke-width="2"/>
                            <line x1="18.36" y1="5.64" x2="19.78" y2="4.22" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- 导航标签 -->
        <nav class="settings-nav">
            <button class="nav-tab active" data-tab="api">API配置</button>
            <button class="nav-tab" data-tab="templates">提示词模板</button>
            <button class="nav-tab" data-tab="ui">界面设置</button>
            <button class="nav-tab" data-tab="dingtalk">钉钉集成</button>
            <button class="nav-tab" data-tab="advanced">高级设置</button>
        </nav>

        <!-- 主要内容区域 -->
        <main class="settings-main">
            <!-- API配置标签页 -->
            <section class="tab-content active" id="api-tab">
                <div class="section-header">
                    <h2>API配置</h2>
                    <p>配置AI服务提供商和API密钥</p>
                </div>

                <div class="form-group">
                    <label for="apiProvider">API提供商</label>
                    <select id="apiProvider" class="form-select">
                        <option value="qwen">通义千问 (推荐)</option>
                        <option value="openai">OpenAI兼容</option>
                    </select>
                    <small class="form-help">选择您要使用的AI服务提供商</small>
                </div>

                <div class="form-group">
                    <label for="apiKey">API密钥</label>
                    <input type="password" id="apiKey" class="form-input" placeholder="API密钥已内置" readonly>
                    <small class="form-help">
                        API密钥已内置，无需手动配置
                    </small>
                </div>

                <div class="form-group">
                    <label for="baseUrl">API端点</label>
                    <input type="url" id="baseUrl" class="form-input" placeholder="API端点已内置" readonly>
                    <small class="form-help">
                        API端点已内置，无需手动配置
                    </small>
                </div>

                <div class="form-group">
                    <label for="model">模型</label>
                    <select id="model" class="form-select">
                        <option value="qwen-plus">qwen-plus (推荐)</option>
                        <option value="qwen-turbo">qwen-turbo</option>
                        <option value="qwen-max">qwen-max</option>
                        <option value="qwen-long">qwen-long</option>
                    </select>
                    <small class="form-help">选择要使用的AI模型</small>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="temperature">创造性 (Temperature)</label>
                        <input type="range" id="temperature" class="form-range" min="0" max="2" step="0.1" value="0.7">
                        <div class="range-value" id="temperatureValue">0.7</div>
                        <small class="form-help">控制回答的创造性，0为最保守，2为最创新</small>
                    </div>
                    <div class="form-group">
                        <label for="maxTokens">最大输出长度</label>
                        <input type="number" id="maxTokens" class="form-input" min="100" max="4000" value="2000">
                        <small class="form-help">限制AI回答的最大长度</small>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" id="testConnection">测试连接</button>
                    <button type="button" class="btn btn-primary" id="saveApiConfig">保存配置</button>
                </div>

                <div class="connection-status" id="connectionStatus" style="display: none;">
                    <div class="status-icon"></div>
                    <div class="status-message"></div>
                </div>
            </section>

            <!-- 提示词模板标签页 -->
            <section class="tab-content" id="templates-tab">
                <div class="section-header">
                    <h2>提示词模板</h2>
                    <p>管理和自定义AI总结的提示词模板</p>
                </div>

                <div class="templates-toolbar">
                    <button class="btn btn-primary" id="addTemplate">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <line x1="12" y1="5" x2="12" y2="19" stroke="currentColor" stroke-width="2"/>
                            <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        添加模板
                    </button>
                    <button class="btn btn-secondary" id="importTemplates">导入模板</button>
                    <button class="btn btn-secondary" id="exportTemplates">导出模板</button>
                </div>

                <div class="templates-list" id="templatesList">
                    <!-- 模板列表将在这里动态生成 -->
                </div>

                <!-- 模板编辑器 -->
                <div class="template-editor" id="templateEditor" style="display: none;">
                    <div class="editor-header">
                        <h3 id="editorTitle">编辑模板</h3>
                        <button class="icon-btn" id="closeEditor">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2"/>
                                <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </button>
                    </div>
                    <div class="editor-content">
                        <div class="form-group">
                            <label for="templateName">模板名称</label>
                            <input type="text" id="templateName" class="form-input" placeholder="输入模板名称">
                        </div>
                        <div class="form-group">
                            <label for="templateDescription">模板描述</label>
                            <input type="text" id="templateDescription" class="form-input" placeholder="输入模板描述">
                        </div>
                        <div class="form-group">
                            <label for="templatePrompt">提示词内容</label>
                            <textarea id="templatePrompt" class="form-textarea" rows="8" placeholder="输入提示词内容，使用 {content} 作为网页内容的占位符"></textarea>
                            <small class="form-help">使用 {content} 作为网页内容的占位符</small>
                        </div>
                        <div class="editor-actions">
                            <button class="btn btn-secondary" id="previewTemplate">预览效果</button>
                            <button class="btn btn-primary" id="saveTemplate">保存模板</button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 界面设置标签页 -->
            <section class="tab-content" id="ui-tab">
                <div class="section-header">
                    <h2>界面设置</h2>
                    <p>自定义扩展的外观和行为</p>
                </div>

                <div class="form-group">
                    <label for="themeSelect">主题</label>
                    <select id="themeSelect" class="form-select">
                        <option value="dark">深色主题</option>
                        <option value="light">浅色主题</option>
                        <option value="auto">跟随系统</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="sidebarWidth">侧边栏宽度</label>
                    <input type="range" id="sidebarWidth" class="form-range" min="300" max="500" step="10" value="380">
                    <div class="range-value" id="sidebarWidthValue">380px</div>
                </div>

                <div class="form-group">
                    <div class="checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="showWordCount" class="form-checkbox">
                            <span class="checkbox-custom"></span>
                            显示字数统计
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="enableNotifications" class="form-checkbox">
                            <span class="checkbox-custom"></span>
                            启用通知提醒
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="autoSummarize" class="form-checkbox">
                            <span class="checkbox-custom"></span>
                            自动总结页面（实验性）
                        </label>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn btn-primary" id="saveUISettings">保存设置</button>
                </div>
            </section>

            <!-- 钉钉集成标签页 -->
            <section class="tab-content" id="dingtalk-tab">
                <div class="section-header">
                    <h2>钉钉集成</h2>
                    <p>配置钉钉账户集成和组织权限</p>
                </div>

                <!-- 认证状态显示 -->
                <div class="settings-group">
                    <h3>认证状态</h3>
                    <div class="dingtalk-auth-display" id="dingTalkAuthDisplay">
                        <!-- 未认证状态 -->
                        <div class="auth-status-card auth-not-authenticated" id="authNotAuthenticated">
                            <div class="auth-status-icon">
                                <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                                    <circle cx="12" cy="12" r="10" stroke="#ff4d4f" stroke-width="2"/>
                                    <line x1="15" y1="9" x2="9" y2="15" stroke="#ff4d4f" stroke-width="2"/>
                                    <line x1="9" y1="9" x2="15" y2="15" stroke="#ff4d4f" stroke-width="2"/>
                                </svg>
                            </div>
                            <div class="auth-status-content">
                                <h4>未登录钉钉账户</h4>
                                <p>请登录钉钉账户以使用集成功能</p>
                                <button class="btn btn-primary" id="dingTalkLoginBtn">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                        <path d="M15 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <polyline points="10,17 15,12 10,7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <line x1="15" y1="12" x2="3" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    登录钉钉
                                </button>
                            </div>
                        </div>

                        <!-- 已认证状态 -->
                        <div class="auth-status-card auth-authenticated" id="authAuthenticated" style="display: none;">
                            <div class="auth-status-icon">
                                <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                                    <circle cx="12" cy="12" r="10" stroke="#52c41a" stroke-width="2"/>
                                    <path d="M9 12L11 14L15 10" stroke="#52c41a" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </div>
                            <div class="auth-status-content">
                                <h4>已登录钉钉账户</h4>
                                <div class="auth-user-details">
                                    <div class="user-info">
                                        <div class="user-avatar" id="dingTalkUserAvatar">
                                            <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                                                <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                                            </svg>
                                        </div>
                                        <div class="user-details">
                                            <div class="user-name" id="dingTalkUserName">钉钉用户</div>
                                            <div class="user-email" id="dingTalkUserEmail"><EMAIL></div>
                                        </div>
                                    </div>
                                    <div class="auth-actions">
                                        <button class="btn btn-secondary" id="refreshAuthBtn">刷新状态</button>
                                        <button class="btn btn-danger" id="dingTalkLogoutBtn">退出登录</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 组织选择 -->
                <div class="settings-group" id="orgSelectionGroup" style="display: none;">
                    <h3>组织选择</h3>
                    <div class="form-group">
                        <label for="dingTalkOrgSelect">选择要使用的组织</label>
                        <select id="dingTalkOrgSelect" class="form-select">
                            <option value="">请选择组织</option>
                        </select>
                        <small class="form-help">选择您要在此扩展中使用的钉钉组织</small>
                    </div>
                    <div class="org-permissions" id="orgPermissions">
                        <!-- 组织权限信息将在这里显示 -->
                    </div>
                </div>

                <!-- 集成配置 -->
                <div class="settings-group" id="integrationConfigGroup" style="display: none;">
                    <h3>集成配置</h3>
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="enableDingTalkIntegration" checked>
                            <span class="checkbox-custom"></span>
                            启用钉钉集成功能
                        </label>
                        <small class="form-help">关闭后将禁用所有钉钉相关功能</small>
                    </div>
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="autoSyncUserInfo">
                            <span class="checkbox-custom"></span>
                            自动同步用户信息
                        </label>
                        <small class="form-help">自动同步钉钉用户信息和组织变更</small>
                    </div>
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="showAuthStatusInSidebar" checked>
                            <span class="checkbox-custom"></span>
                            在侧边栏显示认证状态
                        </label>
                        <small class="form-help">在侧边栏中显示钉钉认证状态信息</small>
                    </div>
                </div>

                <!-- 环境配置 -->
                <div class="settings-group">
                    <h3>环境配置</h3>
                    <div class="form-group">
                        <label for="dingTalkEnvironment">钉钉环境</label>
                        <select id="dingTalkEnvironment" class="form-select">
                            <option value="production">生产环境 (docs.dingtalk.com)</option>
                            <option value="pre">预发环境 (pre-docs.dingtalk.com)</option>
                        </select>
                        <small class="form-help">选择要连接的钉钉环境，通常使用生产环境</small>
                    </div>
                </div>
            </section>

            <!-- 高级设置标签页 -->
            <section class="tab-content" id="advanced-tab">
                <div class="section-header">
                    <h2>高级设置</h2>
                    <p>数据管理和高级功能配置</p>
                </div>

                <div class="settings-group">
                    <h3>数据管理</h3>
                    <div class="form-actions">
                        <button class="btn btn-secondary" id="exportConfig">导出配置</button>
                        <button class="btn btn-secondary" id="importConfig">导入配置</button>
                        <button class="btn btn-secondary" id="clearHistory">清空历史</button>
                        <button class="btn btn-danger" id="resetSettings">重置所有设置</button>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>关于</h3>
                    <div class="about-info">
                        <p><strong>版本:</strong> 1.0.0</p>
                        <p><strong>开发者:</strong> 智能总结助手团队</p>
                        <p><strong>技术支持:</strong> 通义千问 API</p>
                        <p><strong>更新时间:</strong> 2024年</p>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Toast 通知 -->
    <div class="toast" id="toast">
        <div class="toast-content">
            <div class="toast-icon" id="toastIcon"></div>
            <span class="toast-message" id="toastMessage"></span>
        </div>
    </div>

    <!-- 确认对话框 -->
    <div class="modal-overlay" id="confirmModal" style="display: none;">
        <div class="modal">
            <div class="modal-header">
                <h3 id="confirmTitle">确认操作</h3>
            </div>
            <div class="modal-body">
                <p id="confirmMessage">您确定要执行此操作吗？</p>
            </div>
            <div class="modal-actions">
                <button class="btn btn-secondary" id="confirmCancel">取消</button>
                <button class="btn btn-primary" id="confirmOk">确定</button>
            </div>
        </div>
    </div>

</script>
    <script src="options.js"></script>
</body>
</html>
