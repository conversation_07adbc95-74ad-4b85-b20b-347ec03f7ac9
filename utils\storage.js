// 智能网页总结助手 - 存储工具类
// 处理Chrome扩展的数据存储

class StorageManager {
  constructor() {
    this.storageArea = chrome.storage.sync; // 使用同步存储
    this.localStorageArea = chrome.storage.local; // 使用本地存储
  }

  // 获取配置数据
  async getConfig() {
    try {
      const data = await this.get();
      return {
        apiConfig: data.apiConfig || this.getDefaultAPIConfig(),
        promptTemplates: data.promptTemplates || this.getDefaultPromptTemplates(),
        selectedTemplate: data.selectedTemplate || 'default',
        uiSettings: data.uiSettings || this.getDefaultUISettings(),
        dingTalkConfig: data.dingTalkConfig || this.getDefaultDingTalkConfig(),
        history: data.history || []
      };
    } catch (error) {
      console.error('获取配置失败:', error);
      return this.getDefaultConfig();
    }
  }

  // 保存配置数据
  async saveConfig(config) {
    try {
      await this.set(config);
      console.log('配置保存成功');
      return { success: true };
    } catch (error) {
      console.error('保存配置失败:', error);
      return { success: false, error: error.message };
    }
  }

  // 获取API配置
  async getAPIConfig() {
    const config = await this.getConfig();
    return config.apiConfig;
  }

  // 保存API配置
  async saveAPIConfig(apiConfig) {
    return this.saveConfig({ apiConfig });
  }

  // 获取提示词模板
  async getPromptTemplates() {
    const config = await this.getConfig();
    return config.promptTemplates;
  }

  // 保存提示词模板
  async savePromptTemplates(promptTemplates) {
    return this.saveConfig({ promptTemplates });
  }

  // 添加自定义提示词模板
  async addPromptTemplate(key, template) {
    const templates = await this.getPromptTemplates();
    templates[key] = template;
    return this.savePromptTemplates(templates);
  }

  // 删除提示词模板
  async deletePromptTemplate(key) {
    const templates = await this.getPromptTemplates();
    if (templates[key] && key !== 'default') {
      delete templates[key];
      return this.savePromptTemplates(templates);
    }
    return { success: false, error: '无法删除默认模板' };
  }

  // 获取UI设置
  async getUISettings() {
    const config = await this.getConfig();
    return config.uiSettings;
  }

  // 保存UI设置
  async saveUISettings(uiSettings) {
    return this.saveConfig({ uiSettings });
  }

  // 获取总结历史
  async getHistory() {
    const config = await this.getConfig();
    return config.history || [];
  }

  // 添加总结历史记录
  async addHistoryRecord(record) {
    try {
      const history = await this.getHistory();
      const newRecord = {
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        ...record
      };
      
      // 限制历史记录数量（最多保存100条）
      history.unshift(newRecord);
      if (history.length > 100) {
        history.splice(100);
      }
      
      await this.saveConfig({ history });
      return { success: true, record: newRecord };
    } catch (error) {
      console.error('添加历史记录失败:', error);
      return { success: false, error: error.message };
    }
  }

  // 删除历史记录
  async deleteHistoryRecord(id) {
    try {
      const history = await this.getHistory();
      const filteredHistory = history.filter(record => record.id !== id);
      await this.saveConfig({ history: filteredHistory });
      return { success: true };
    } catch (error) {
      console.error('删除历史记录失败:', error);
      return { success: false, error: error.message };
    }
  }

  // 清空历史记录
  async clearHistory() {
    try {
      await this.saveConfig({ history: [] });
      return { success: true };
    } catch (error) {
      console.error('清空历史记录失败:', error);
      return { success: false, error: error.message };
    }
  }

  // 导出配置
  async exportConfig() {
    try {
      const config = await this.getConfig();
      const exportData = {
        version: '1.0.0',
        exportTime: new Date().toISOString(),
        config: {
          promptTemplates: config.promptTemplates,
          selectedTemplate: config.selectedTemplate,
          uiSettings: config.uiSettings
          // 不导出API配置和历史记录（安全考虑）
        }
      };
      
      return { success: true, data: exportData };
    } catch (error) {
      console.error('导出配置失败:', error);
      return { success: false, error: error.message };
    }
  }

  // 导入配置
  async importConfig(importData) {
    try {
      if (!importData || !importData.config) {
        throw new Error('导入数据格式错误');
      }
      
      const currentConfig = await this.getConfig();
      const newConfig = {
        ...currentConfig,
        ...importData.config
      };
      
      await this.saveConfig(newConfig);
      return { success: true };
    } catch (error) {
      console.error('导入配置失败:', error);
      return { success: false, error: error.message };
    }
  }

  // 重置为默认配置
  async resetToDefault() {
    try {
      const defaultConfig = this.getDefaultConfig();
      await this.clear(); // 清空所有数据
      await this.saveConfig(defaultConfig);
      return { success: true };
    } catch (error) {
      console.error('重置配置失败:', error);
      return { success: false, error: error.message };
    }
  }

  // 获取默认配置
  getDefaultConfig() {
    return {
      apiConfig: this.getDefaultAPIConfig(),
      promptTemplates: this.getDefaultPromptTemplates(),
      selectedTemplate: 'default',
      uiSettings: this.getDefaultUISettings(),
      history: []
    };
  }

  // 获取默认API配置
  getDefaultAPIConfig() {
    return {
      provider: 'qwen',
      apiKey: '',
      baseUrl: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
      model: 'qwen-plus',
      temperature: 0.7,
      maxTokens: 2000
    };
  }

  // 获取默认提示词模板
  getDefaultPromptTemplates() {
    return {
      default: {
        name: '默认总结',
        prompt: '请对以下内容进行总结，提取关键信息和要点：\n\n{content}',
        description: '通用的内容总结模板'
      },
      news: {
        name: '新闻总结',
        prompt: '请总结这篇新闻的主要内容，包括：\n1. 核心事件\n2. 关键人物\n3. 时间地点\n4. 影响和意义\n\n内容：{content}',
        description: '适用于新闻文章的总结'
      },
      academic: {
        name: '学术文章',
        prompt: '请总结这篇学术文章，包括：\n1. 研究目的和问题\n2. 主要方法\n3. 核心发现\n4. 结论和意义\n\n内容：{content}',
        description: '适用于学术论文和研究文章'
      },
      technical: {
        name: '技术文档',
        prompt: '请总结这份技术文档的要点：\n1. 主要功能特性\n2. 使用方法\n3. 注意事项\n4. 适用场景\n\n内容：{content}',
        description: '适用于技术文档和教程'
      },
      meeting: {
        name: '会议纪要',
        prompt: '请整理这份会议内容，包括：\n1. 会议主题\n2. 主要讨论点\n3. 决策事项\n4. 后续行动\n\n内容：{content}',
        description: '适用于会议记录和纪要'
      }
    };
  }

  // 获取默认UI设置
  getDefaultUISettings() {
    return {
      theme: 'dark',
      sidebarWidth: 380,
      autoSummarize: false,
      showWordCount: true,
      enableNotifications: true,
      language: 'zh-CN'
    };
  }

  // 获取默认钉钉配置
  getDefaultDingTalkConfig() {
    return {
      enabled: true,
      autoLogin: false,
      environment: 'production',
      showAuthStatus: true,
      syncUserInfo: true
    };
  }

  // 底层存储方法
  async get(keys = null) {
    return new Promise((resolve, reject) => {
      this.storageArea.get(keys, (result) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(result);
        }
      });
    });
  }

  async set(data) {
    return new Promise((resolve, reject) => {
      this.storageArea.set(data, () => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    });
  }

  async remove(keys) {
    return new Promise((resolve, reject) => {
      this.storageArea.remove(keys, () => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    });
  }

  async clear() {
    return new Promise((resolve, reject) => {
      this.storageArea.clear(() => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    });
  }

  // 本地存储方法（用于大量数据）
  async getLocal(keys = null) {
    return new Promise((resolve, reject) => {
      this.localStorageArea.get(keys, (result) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(result);
        }
      });
    });
  }

  async setLocal(data) {
    return new Promise((resolve, reject) => {
      this.localStorageArea.set(data, () => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    });
  }

  async removeLocal(keys) {
    return new Promise((resolve, reject) => {
      this.localStorageArea.remove(keys, () => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    });
  }

  async clearLocal() {
    return new Promise((resolve, reject) => {
      this.localStorageArea.clear(() => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    });
  }

  // ==================== 钉钉认证相关存储方法 ====================

  // 钉钉认证存储键定义
  static DINGTALK_KEYS = {
    LOGIN_STATUS: 'dingtalk_login_status',
    USER_INFO: 'dingtalk_user_info',
    ORGANIZATIONS: 'dingtalk_organizations',
    SELECTED_CORP: 'dingtalk_selected_corp',
    AUTH_TIMESTAMP: 'dingtalk_auth_timestamp',
    CONFIG: 'dingtalk_config'
  };

  // 保存钉钉认证数据
  async saveDingTalkAuth(authData) {
    try {
      const data = {
        [StorageManager.DINGTALK_KEYS.LOGIN_STATUS]: authData.isAuthenticated || false,
        [StorageManager.DINGTALK_KEYS.USER_INFO]: authData.userInfo || null,
        [StorageManager.DINGTALK_KEYS.ORGANIZATIONS]: authData.organizations || [],
        [StorageManager.DINGTALK_KEYS.SELECTED_CORP]: authData.selectedCorpId || null,
        [StorageManager.DINGTALK_KEYS.AUTH_TIMESTAMP]: Date.now()
      };

      await this.setLocal(data);
      console.log('钉钉认证数据已保存');

    } catch (error) {
      console.error('保存钉钉认证数据失败:', error);
      throw error;
    }
  }

  // 加载钉钉认证数据
  async loadDingTalkAuth() {
    try {
      const data = await this.getLocal(Object.values(StorageManager.DINGTALK_KEYS));

      const authData = {
        isAuthenticated: data[StorageManager.DINGTALK_KEYS.LOGIN_STATUS] || false,
        userInfo: data[StorageManager.DINGTALK_KEYS.USER_INFO] || null,
        organizations: data[StorageManager.DINGTALK_KEYS.ORGANIZATIONS] || [],
        selectedCorpId: data[StorageManager.DINGTALK_KEYS.SELECTED_CORP] || null,
        authTimestamp: data[StorageManager.DINGTALK_KEYS.AUTH_TIMESTAMP] || null
      };

      // 检查认证是否过期（24小时）
      if (authData.authTimestamp) {
        const now = Date.now();
        const authAge = now - authData.authTimestamp;
        const maxAge = 24 * 60 * 60 * 1000; // 24小时

        if (authAge > maxAge) {
          console.log('钉钉认证数据已过期，清理本地状态');
          await this.clearDingTalkAuth();
          return this.getDefaultDingTalkAuthData();
        }
      }

      return authData;

    } catch (error) {
      console.error('加载钉钉认证数据失败:', error);
      return this.getDefaultDingTalkAuthData();
    }
  }

  // 清理钉钉认证数据
  async clearDingTalkAuth() {
    try {
      await this.removeLocal(Object.values(StorageManager.DINGTALK_KEYS));
      console.log('钉钉认证数据已清理');

    } catch (error) {
      console.error('清理钉钉认证数据失败:', error);
      throw error;
    }
  }

  // 获取默认钉钉认证数据
  getDefaultDingTalkAuthData() {
    return {
      isAuthenticated: false,
      userInfo: null,
      organizations: [],
      selectedCorpId: null,
      authTimestamp: null
    };
  }

  // 保存钉钉配置
  async saveDingTalkConfig(config) {
    try {
      await this.set({
        [StorageManager.DINGTALK_KEYS.CONFIG]: config
      });
      console.log('钉钉配置已保存');

    } catch (error) {
      console.error('保存钉钉配置失败:', error);
      throw error;
    }
  }

  // 加载钉钉配置
  async loadDingTalkConfig() {
    try {
      const data = await this.get([StorageManager.DINGTALK_KEYS.CONFIG]);
      return data[StorageManager.DINGTALK_KEYS.CONFIG] || this.getDefaultDingTalkConfig();

    } catch (error) {
      console.error('加载钉钉配置失败:', error);
      return this.getDefaultDingTalkConfig();
    }
  }
}

// 导出存储管理器类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = StorageManager;
} else if (typeof window !== 'undefined') {
  window.StorageManager = StorageManager;
}
