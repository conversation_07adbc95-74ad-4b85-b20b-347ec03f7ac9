// 智能网页总结助手 - 内容脚本
// 负责从网页中提取主要文本内容

class ContentExtractor {
  constructor() {
    this.initializeExtractor();
  }

  // 初始化内容提取器
  initializeExtractor() {
    // 监听来自background script的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      if (request.action === 'extractContent') {
        try {
          const content = this.extractPageContent();
          sendResponse({ success: true, content: content });
        } catch (error) {
          console.error('内容提取失败:', error);
          sendResponse({ success: false, error: error.message });
        }
      }
      return true;
    });

    // 页面加载完成后自动提取内容（可选）
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        this.cachePageContent();
      });
    } else {
      this.cachePageContent();
    }
  }

  // 提取页面主要内容
  extractPageContent() {
    console.log('开始提取页面内容...');
    
    // 创建页面内容的副本以避免修改原页面
    const pageClone = document.cloneNode(true);
    
    // 移除不需要的元素
    this.removeUnwantedElements(pageClone);
    
    // 查找主要内容区域
    const mainContent = this.findMainContent(pageClone);
    
    // 提取和清理文本
    const textContent = this.extractAndCleanText(mainContent);
    
    // 分析内容结构
    const contentAnalysis = this.analyzeContent(mainContent);
    
    const result = {
      content: textContent,
      title: document.title,
      url: window.location.href,
      wordCount: this.countWords(textContent),
      language: this.detectLanguage(textContent),
      structure: contentAnalysis,
      extractedAt: new Date().toISOString()
    };
    
    console.log('内容提取完成:', result);
    return result;
  }

  // 移除不需要的元素
  removeUnwantedElements(doc) {
    const unwantedSelectors = [
      // 脚本和样式
      'script', 'style', 'noscript',
      
      // 导航和菜单
      'nav', 'header', 'footer', 'aside',
      '.navigation', '.nav', '.menu', '.sidebar',
      '.breadcrumb', '.breadcrumbs',
      
      // 广告和推广
      '.advertisement', '.ads', '.ad', '.advert',
      '.sponsored', '.promotion', '.banner',
      '[class*="ad-"]', '[id*="ad-"]',
      '[class*="ads-"]', '[id*="ads-"]',
      
      // 社交和分享
      '.social', '.social-share', '.share-buttons',
      '.social-media', '.follow-us',
      
      // 评论和互动
      '.comments', '.comment', '.discussion',
      '.reviews', '.rating', '.feedback',
      
      // 弹窗和模态框
      '.popup', '.modal', '.overlay', '.lightbox',
      '.newsletter', '.subscription',
      
      // 相关内容和推荐
      '.related', '.recommended', '.suggestions',
      '.more-stories', '.you-might-like',
      
      // 工具栏和控件
      '.toolbar', '.controls', '.player-controls',
      '.video-controls', '.audio-controls',
      
      // 其他干扰元素
      '.cookie-notice', '.gdpr-notice',
      '.loading', '.spinner', '.placeholder',
      'iframe', 'embed', 'object'
    ];

    unwantedSelectors.forEach(selector => {
      try {
        const elements = doc.querySelectorAll(selector);
        elements.forEach(element => {
          if (element && element.parentNode) {
            element.parentNode.removeChild(element);
          }
        });
      } catch (error) {
        console.warn(`移除元素失败: ${selector}`, error);
      }
    });

    // 移除隐藏元素
    this.removeHiddenElements(doc);
    
    // 移除空元素
    this.removeEmptyElements(doc);
  }

  // 移除隐藏元素
  removeHiddenElements(doc) {
    const allElements = doc.querySelectorAll('*');
    allElements.forEach(element => {
      try {
        const style = window.getComputedStyle(element);
        if (style.display === 'none' || 
            style.visibility === 'hidden' || 
            style.opacity === '0' ||
            element.hidden) {
          if (element.parentNode) {
            element.parentNode.removeChild(element);
          }
        }
      } catch (error) {
        // 忽略样式获取错误
      }
    });
  }

  // 移除空元素
  removeEmptyElements(doc) {
    const emptyElements = doc.querySelectorAll('div, span, p, section, article');
    emptyElements.forEach(element => {
      if (element.textContent.trim() === '' && 
          element.children.length === 0) {
        if (element.parentNode) {
          element.parentNode.removeChild(element);
        }
      }
    });
  }

  // 查找主要内容区域
  findMainContent(doc) {
    // 按优先级尝试不同的选择器
    const contentSelectors = [
      // 语义化标签
      'main',
      'article',
      '[role="main"]',
      
      // 常见的内容类名
      '.content',
      '.main-content',
      '.post-content',
      '.article-content',
      '.entry-content',
      '.page-content',
      
      // 新闻网站常用
      '.story-body',
      '.article-body',
      '.post-body',
      '.news-content',
      
      // 博客常用
      '.post',
      '.entry',
      '.blog-post',
      
      // 通用ID
      '#content',
      '#main',
      '#main-content',
      '#article',
      '#post'
    ];

    for (const selector of contentSelectors) {
      const element = doc.querySelector(selector);
      if (element && this.hasSignificantContent(element)) {
        console.log(`找到主要内容区域: ${selector}`);
        return element;
      }
    }

    // 如果没找到特定的内容区域，使用body但进一步过滤
    console.log('使用body作为内容区域');
    return this.filterBodyContent(doc.body || doc.documentElement);
  }

  // 检查元素是否包含有意义的内容
  hasSignificantContent(element) {
    const text = element.textContent || '';
    const wordCount = this.countWords(text);
    return wordCount > 50; // 至少50个词
  }

  // 过滤body内容
  filterBodyContent(body) {
    // 创建一个新的容器
    const filteredContent = document.createElement('div');
    
    // 查找所有可能包含主要内容的元素
    const contentElements = body.querySelectorAll('p, h1, h2, h3, h4, h5, h6, li, blockquote, pre');
    
    contentElements.forEach(element => {
      if (this.isContentElement(element)) {
        filteredContent.appendChild(element.cloneNode(true));
      }
    });
    
    return filteredContent;
  }

  // 判断是否为内容元素
  isContentElement(element) {
    const text = element.textContent || '';
    const wordCount = this.countWords(text);
    
    // 过滤太短的文本
    if (wordCount < 3) return false;
    
    // 过滤导航链接
    if (element.tagName === 'A' && wordCount < 10) return false;
    
    // 过滤可能的菜单项
    const className = element.className || '';
    const id = element.id || '';
    const unwantedPatterns = ['menu', 'nav', 'header', 'footer', 'sidebar', 'ad'];
    
    for (const pattern of unwantedPatterns) {
      if (className.toLowerCase().includes(pattern) || 
          id.toLowerCase().includes(pattern)) {
        return false;
      }
    }
    
    return true;
  }

  // 提取和清理文本
  extractAndCleanText(element) {
    let text = element.textContent || element.innerText || '';
    
    // 清理文本
    text = text
      // 移除多余的空白字符
      .replace(/\s+/g, ' ')
      // 移除多余的换行
      .replace(/\n\s*\n/g, '\n')
      // 移除行首行尾空白
      .replace(/^\s+|\s+$/gm, '')
      // 移除特殊字符（保留基本标点）
      .replace(/[^\w\s\u4e00-\u9fff.,!?;:()[\]{}""''—–-]/g, '')
      .trim();
    
    // 限制内容长度（避免超过API限制）
    const maxLength = 10000;
    if (text.length > maxLength) {
      text = text.substring(0, maxLength) + '...';
      console.log(`内容过长，已截断至${maxLength}字符`);
    }
    
    return text;
  }

  // 分析内容结构
  analyzeContent(element) {
    const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6');
    const paragraphs = element.querySelectorAll('p');
    const lists = element.querySelectorAll('ul, ol');
    const images = element.querySelectorAll('img');
    
    return {
      headingCount: headings.length,
      paragraphCount: paragraphs.length,
      listCount: lists.length,
      imageCount: images.length,
      hasStructure: headings.length > 0
    };
  }

  // 统计词数
  countWords(text) {
    if (!text) return 0;
    
    // 中文字符按字符计算，英文按单词计算
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishWords = text.replace(/[\u4e00-\u9fff]/g, '').match(/\b\w+\b/g);
    const englishWordCount = englishWords ? englishWords.length : 0;
    
    return chineseChars + englishWordCount;
  }

  // 检测语言
  detectLanguage(text) {
    if (!text) return 'unknown';
    
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const totalChars = text.length;
    
    if (chineseChars / totalChars > 0.3) {
      return 'zh';
    } else {
      return 'en';
    }
  }

  // 缓存页面内容（可选功能）
  cachePageContent() {
    try {
      const content = this.extractPageContent();
      // 可以将内容缓存到localStorage或发送给background script
      console.log('页面内容已缓存');
    } catch (error) {
      console.error('缓存页面内容失败:', error);
    }
  }
}

// 初始化内容提取器
if (typeof window !== 'undefined') {
  new ContentExtractor();
}
