// 智能网页总结助手 - API工具类
// 处理与AI服务的通信

class AIAPIClient {
  constructor(config) {
    this.config = config;
    this.retryCount = 3;
    this.retryDelay = 1000; // 1秒
  }

  // 更新配置
  updateConfig(config) {
    this.config = { ...this.config, ...config };
  }

  // 调用通义千问API
  async callQwenAPI(messages, options = {}) {
    const requestBody = {
      model: this.config.model || 'qwen-plus',
      messages: messages,
      temperature: options.temperature || 0.7,
      max_tokens: options.maxTokens || 2000,
      top_p: options.topP || 0.8,
      stream: false
    };

    const requestOptions = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`
      },
      body: JSON.stringify(requestBody)
    };

    return this.makeRequest(`${this.config.baseUrl}/chat/completions`, requestOptions);
  }

  // 调用OpenAI兼容API
  async callOpenAICompatibleAPI(messages, options = {}) {
    const requestBody = {
      model: this.config.model || 'gpt-3.5-turbo',
      messages: messages,
      temperature: options.temperature || 0.7,
      max_tokens: options.maxTokens || 2000,
      top_p: options.topP || 0.8
    };

    const requestOptions = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`
      },
      body: JSON.stringify(requestBody)
    };

    return this.makeRequest(`${this.config.baseUrl}/chat/completions`, requestOptions);
  }

  // 通用API调用方法
  async callAPI(prompt, options = {}) {
    const messages = [
      {
        role: 'system',
        content: '你是一个专业的内容总结助手，能够准确提取和总结各种类型文档的核心信息。请用中文回答。'
      },
      {
        role: 'user',
        content: prompt
      }
    ];

    try {
      let response;
      
      // 根据配置选择API提供商
      switch (this.config.provider) {
        case 'qwen':
          response = await this.callQwenAPI(messages, options);
          break;
        case 'openai':
          response = await this.callOpenAICompatibleAPI(messages, options);
          break;
        default:
          throw new Error(`不支持的API提供商: ${this.config.provider}`);
      }

      return this.extractResponseContent(response);
    } catch (error) {
      console.error('API调用失败:', error);
      throw new Error(`AI服务调用失败: ${error.message}`);
    }
  }

  // 发起HTTP请求（带重试机制）
  async makeRequest(url, options, retryCount = 0) {
    try {
      console.log(`发起API请求 (尝试 ${retryCount + 1}/${this.retryCount + 1}):`, url);
      
      const response = await fetch(url, options);
      
      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        
        try {
          const errorData = JSON.parse(errorText);
          if (errorData.error && errorData.error.message) {
            errorMessage = errorData.error.message;
          } else if (errorData.message) {
            errorMessage = errorData.message;
          }
        } catch (e) {
          // 如果不是JSON格式，使用原始错误文本
          if (errorText) {
            errorMessage += ` - ${errorText}`;
          }
        }
        
        throw new Error(errorMessage);
      }

      const data = await response.json();
      console.log('API请求成功');
      return data;
      
    } catch (error) {
      console.error(`API请求失败 (尝试 ${retryCount + 1}):`, error);
      
      // 如果还有重试次数，则重试
      if (retryCount < this.retryCount) {
        console.log(`等待 ${this.retryDelay}ms 后重试...`);
        await this.delay(this.retryDelay);
        return this.makeRequest(url, options, retryCount + 1);
      }
      
      throw error;
    }
  }

  // 提取响应内容
  extractResponseContent(response) {
    try {
      if (response.choices && response.choices.length > 0) {
        const choice = response.choices[0];
        if (choice.message && choice.message.content) {
          return choice.message.content.trim();
        }
      }
      
      // 兼容其他响应格式
      if (response.output && response.output.choices && response.output.choices.length > 0) {
        const choice = response.output.choices[0];
        if (choice.message && choice.message.content) {
          return choice.message.content.trim();
        }
      }
      
      throw new Error('无法从API响应中提取内容');
    } catch (error) {
      console.error('提取响应内容失败:', error);
      throw new Error('API响应格式错误');
    }
  }

  // 测试API连接
  async testConnection() {
    try {
      const testPrompt = '请回复"连接测试成功"';
      const response = await this.callAPI(testPrompt, { maxTokens: 50 });
      
      if (response && response.includes('连接测试成功')) {
        return { success: true, message: 'API连接测试成功' };
      } else {
        return { success: true, message: 'API连接正常，但响应内容异常', response };
      }
    } catch (error) {
      return { success: false, message: error.message };
    }
  }

  // 获取模型列表（如果API支持）
  async getAvailableModels() {
    try {
      const response = await this.makeRequest(`${this.config.baseUrl}/models`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`
        }
      });
      
      if (response.data && Array.isArray(response.data)) {
        return response.data.map(model => ({
          id: model.id,
          name: model.id,
          description: model.description || ''
        }));
      }
      
      return [];
    } catch (error) {
      console.warn('获取模型列表失败:', error);
      return [];
    }
  }

  // 估算token数量（简单估算）
  estimateTokens(text) {
    if (!text) return 0;
    
    // 中文字符大约1个token，英文单词大约0.75个token
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishWords = text.replace(/[\u4e00-\u9fff]/g, '').match(/\b\w+\b/g);
    const englishWordCount = englishWords ? englishWords.length : 0;
    
    return Math.ceil(chineseChars + englishWordCount * 0.75);
  }

  // 检查内容长度是否超过限制
  checkContentLength(content, maxTokens = 8000) {
    const estimatedTokens = this.estimateTokens(content);
    
    if (estimatedTokens > maxTokens) {
      return {
        valid: false,
        estimatedTokens,
        maxTokens,
        message: `内容过长，预估${estimatedTokens}个token，超过限制${maxTokens}个token`
      };
    }
    
    return {
      valid: true,
      estimatedTokens,
      maxTokens
    };
  }

  // 截断内容到指定token数量
  truncateContent(content, maxTokens = 8000) {
    const lengthCheck = this.checkContentLength(content, maxTokens);
    
    if (lengthCheck.valid) {
      return content;
    }
    
    // 简单截断（按字符比例）
    const ratio = maxTokens / lengthCheck.estimatedTokens;
    const truncatedLength = Math.floor(content.length * ratio * 0.9); // 留10%余量
    
    return content.substring(0, truncatedLength) + '...';
  }

  // 延迟函数
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 格式化错误信息
  formatError(error) {
    if (error.message.includes('401')) {
      return 'API密钥无效，请检查配置';
    } else if (error.message.includes('403')) {
      return 'API访问被拒绝，请检查权限';
    } else if (error.message.includes('429')) {
      return 'API调用频率超限，请稍后重试';
    } else if (error.message.includes('500')) {
      return 'API服务器错误，请稍后重试';
    } else if (error.message.includes('network')) {
      return '网络连接错误，请检查网络';
    } else {
      return error.message || '未知错误';
    }
  }
}

// 导出API客户端类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AIAPIClient;
} else if (typeof window !== 'undefined') {
  window.AIAPIClient = AIAPIClient;
}
