// Markdown预览窗口脚本

class MarkdownPreviewWindow {
  constructor() {
    this.markdownContent = '';
    this.pageInfo = null;
    this.markdownParser = new MarkdownParser();
    
    this.initializeElements();
    this.bindEvents();
    this.loadContent();
  }

  // 初始化DOM元素
  initializeElements() {
    this.previewContent = document.getElementById('previewContent');
    this.metaInfo = document.getElementById('metaInfo');
    this.loadingOverlay = document.getElementById('loadingOverlay');
    
    this.copyBtn = document.getElementById('copyBtn');
    this.downloadBtn = document.getElementById('downloadBtn');
    this.closeBtn = document.getElementById('closeBtn');
  }

  // 绑定事件
  bindEvents() {
    this.copyBtn.addEventListener('click', () => this.copyMarkdown());
    this.downloadBtn.addEventListener('click', () => this.downloadMarkdown());
    this.closeBtn.addEventListener('click', () => this.closeWindow());
    
    // 键盘快捷键
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.closeWindow();
      } else if (e.ctrlKey && e.key === 'c') {
        e.preventDefault();
        this.copyMarkdown();
      } else if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        this.downloadMarkdown();
      }
    });

    // 监听来自父窗口的消息
    window.addEventListener('message', (event) => {
      if (event.data.type === 'MARKDOWN_CONTENT') {
        this.markdownContent = event.data.content;
        this.pageInfo = event.data.pageInfo;
        this.renderPreview();
      }
    });
  }

  // 从URL参数或localStorage加载内容
  loadContent() {
    this.showLoading();
    
    // 尝试从URL参数获取内容
    const urlParams = new URLSearchParams(window.location.search);
    const contentParam = urlParams.get('content');
    
    if (contentParam) {
      try {
        const data = JSON.parse(decodeURIComponent(contentParam));
        this.markdownContent = data.content || '';
        this.pageInfo = data.pageInfo || null;
        this.renderPreview();
        return;
      } catch (error) {
        console.error('解析URL参数失败:', error);
      }
    }
    
    // 尝试从localStorage获取内容
    try {
      const savedContent = localStorage.getItem('markdown_preview_content');
      const savedPageInfo = localStorage.getItem('markdown_preview_pageinfo');
      
      if (savedContent) {
        this.markdownContent = savedContent;
        this.pageInfo = savedPageInfo ? JSON.parse(savedPageInfo) : null;
        this.renderPreview();
        return;
      }
    } catch (error) {
      console.error('从localStorage加载内容失败:', error);
    }
    
    // 如果没有内容，显示提示
    this.hideLoading();
    this.showEmptyState();
  }

  // 渲染预览
  renderPreview() {
    if (!this.markdownContent) {
      this.showEmptyState();
      return;
    }

    try {
      // 解析Markdown
      const result = this.markdownParser.parseWithTOC(this.markdownContent);
      
      // 渲染内容
      let html = result.html;
      if (result.toc) {
        html = result.toc + html;
      }
      
      this.previewContent.innerHTML = html;
      
      // 更新元信息
      this.updateMetaInfo(result.wordCount);
      
      // 添加目录点击事件
      this.addTOCClickHandlers();
      
      this.hideLoading();
    } catch (error) {
      console.error('渲染预览失败:', error);
      this.showError('渲染预览时发生错误');
    }
  }

  // 更新元信息
  updateMetaInfo(wordCount) {
    const now = new Date().toLocaleString('zh-CN');
    const pageTitle = this.pageInfo?.title || '未知页面';
    const pageUrl = this.pageInfo?.url || '';
    
    this.metaInfo.innerHTML = `
      <span>📄 ${pageTitle}</span>
      <span>🕒 ${now}</span>
      <span>📊 ${wordCount} 字</span>
      ${pageUrl ? `<span>🔗 <a href="${pageUrl}" target="_blank" title="${pageUrl}">${this.truncateUrl(pageUrl)}</a></span>` : ''}
    `;
  }

  // 截断URL显示
  truncateUrl(url) {
    if (url.length <= 30) return url;
    return url.substring(0, 30) + '...';
  }

  // 添加目录点击事件
  addTOCClickHandlers() {
    const tocLinks = this.previewContent.querySelectorAll('.markdown-toc a');
    tocLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const targetId = link.getAttribute('href').substring(1);
        const targetElement = this.previewContent.querySelector(`#${targetId}`);
        if (targetElement) {
          targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      });
    });
  }

  // 复制Markdown内容
  async copyMarkdown() {
    if (!this.markdownContent) {
      this.showToast('没有可复制的内容', 'warning');
      return;
    }

    try {
      await navigator.clipboard.writeText(this.markdownContent);
      this.showToast('Markdown内容已复制到剪贴板', 'success');
    } catch (error) {
      console.error('复制失败:', error);
      this.showToast('复制失败，请手动复制', 'error');
    }
  }

  // 下载Markdown文件
  downloadMarkdown() {
    if (!this.markdownContent) {
      this.showToast('没有可下载的内容', 'warning');
      return;
    }

    try {
      const blob = new Blob([this.markdownContent], { type: 'text/markdown;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = this.generateFilename();
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      
      URL.revokeObjectURL(url);
      this.showToast('Markdown文件下载成功', 'success');
    } catch (error) {
      console.error('下载失败:', error);
      this.showToast('下载失败', 'error');
    }
  }

  // 生成文件名
  generateFilename() {
    const title = this.pageInfo?.title || 'markdown';
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:\-T]/g, '');
    const cleanTitle = title.replace(/[^\w\u4e00-\u9fff]/g, '_').substring(0, 50);
    return `${cleanTitle}_${timestamp}.md`;
  }

  // 关闭窗口
  closeWindow() {
    window.close();
  }

  // 显示加载状态
  showLoading() {
    this.loadingOverlay.style.display = 'flex';
  }

  // 隐藏加载状态
  hideLoading() {
    this.loadingOverlay.style.display = 'none';
  }

  // 显示空状态
  showEmptyState() {
    this.previewContent.innerHTML = `
      <div style="text-align: center; padding: 60px 20px; color: var(--text-muted);">
        <div style="font-size: 48px; margin-bottom: 16px;">📄</div>
        <h3 style="margin-bottom: 8px; color: var(--text-secondary);">没有可预览的内容</h3>
        <p>请先在扩展中生成Markdown内容，然后打开预览窗口。</p>
      </div>
    `;
    this.metaInfo.innerHTML = '<span>等待内容加载...</span>';
  }

  // 显示错误状态
  showError(message) {
    this.previewContent.innerHTML = `
      <div style="text-align: center; padding: 60px 20px; color: var(--danger-color);">
        <div style="font-size: 48px; margin-bottom: 16px;">⚠️</div>
        <h3 style="margin-bottom: 8px;">预览失败</h3>
        <p>${message}</p>
      </div>
    `;
    this.hideLoading();
  }

  // 显示提示消息
  showToast(message, type = 'info') {
    // 创建提示元素
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    // 添加样式
    Object.assign(toast.style, {
      position: 'fixed',
      top: '20px',
      right: '20px',
      padding: '12px 20px',
      borderRadius: '6px',
      color: 'white',
      fontWeight: '500',
      zIndex: '10000',
      opacity: '0',
      transform: 'translateY(-20px)',
      transition: 'all 0.3s ease'
    });
    
    // 设置背景色
    const colors = {
      success: '#059669',
      warning: '#d97706',
      error: '#dc2626',
      info: '#2563eb'
    };
    toast.style.backgroundColor = colors[type] || colors.info;
    
    document.body.appendChild(toast);
    
    // 显示动画
    setTimeout(() => {
      toast.style.opacity = '1';
      toast.style.transform = 'translateY(0)';
    }, 10);
    
    // 自动隐藏
    setTimeout(() => {
      toast.style.opacity = '0';
      toast.style.transform = 'translateY(-20px)';
      setTimeout(() => {
        if (toast.parentNode) {
          toast.parentNode.removeChild(toast);
        }
      }, 300);
    }, 3000);
  }
}

// 初始化预览窗口
document.addEventListener('DOMContentLoaded', () => {
  new MarkdownPreviewWindow();
});
