// 智能网页总结助手 - 设置页面脚本

// 硬编码的API配置
const HARDCODED_API_CONFIG = {
  apiKey: 'sk-466900693bb54313bb9c9a5feb986eb4',
  baseUrl: 'https://dashscope.aliyuncs.com/compatible-mode/v1'
};

class SettingsApp {
  constructor() {
    this.config = null;
    this.currentEditingTemplate = null;
    this.dingTalkAuthStatus = null;

    this.initializeElements();
    this.bindEvents();
    this.loadConfig();
    this.initializeTabs();
    this.initializeDingTalkAuth();
  }

  // 初始化DOM元素引用
  initializeElements() {
    // 导航标签
    this.navTabs = document.querySelectorAll('.nav-tab');
    this.tabContents = document.querySelectorAll('.tab-content');
    
    // 主题切换
    this.themeToggle = document.getElementById('themeToggle');
    
    // API配置元素
    this.apiProvider = document.getElementById('apiProvider');
    this.apiKey = document.getElementById('apiKey');
    this.baseUrl = document.getElementById('baseUrl');
    this.model = document.getElementById('model');
    this.temperature = document.getElementById('temperature');
    this.temperatureValue = document.getElementById('temperatureValue');
    this.maxTokens = document.getElementById('maxTokens');
    this.testConnection = document.getElementById('testConnection');
    this.saveApiConfig = document.getElementById('saveApiConfig');
    this.connectionStatus = document.getElementById('connectionStatus');
    
    // 模板相关元素
    this.addTemplate = document.getElementById('addTemplate');
    this.importTemplates = document.getElementById('importTemplates');
    this.exportTemplates = document.getElementById('exportTemplates');
    this.templatesList = document.getElementById('templatesList');
    this.templateEditor = document.getElementById('templateEditor');
    this.editorTitle = document.getElementById('editorTitle');
    this.closeEditor = document.getElementById('closeEditor');
    this.templateName = document.getElementById('templateName');
    this.templateDescription = document.getElementById('templateDescription');
    this.templatePrompt = document.getElementById('templatePrompt');
    this.previewTemplate = document.getElementById('previewTemplate');
    this.saveTemplate = document.getElementById('saveTemplate');
    
    // UI设置元素
    this.themeSelect = document.getElementById('themeSelect');
    this.sidebarWidth = document.getElementById('sidebarWidth');
    this.sidebarWidthValue = document.getElementById('sidebarWidthValue');
    this.showWordCount = document.getElementById('showWordCount');
    this.enableNotifications = document.getElementById('enableNotifications');
    this.autoSummarize = document.getElementById('autoSummarize');
    this.saveUISettings = document.getElementById('saveUISettings');
    
    // 钉钉集成相关元素
    this.dingTalkAuthDisplay = document.getElementById('dingTalkAuthDisplay');
    this.authNotAuthenticated = document.getElementById('authNotAuthenticated');
    this.authAuthenticated = document.getElementById('authAuthenticated');
    this.dingTalkLoginBtn = document.getElementById('dingTalkLoginBtn');
    this.dingTalkLogoutBtn = document.getElementById('dingTalkLogoutBtn');
    this.refreshAuthBtn = document.getElementById('refreshAuthBtn');
    this.dingTalkUserAvatar = document.getElementById('dingTalkUserAvatar');
    this.dingTalkUserName = document.getElementById('dingTalkUserName');
    this.dingTalkUserEmail = document.getElementById('dingTalkUserEmail');
    this.dingTalkOrgSelect = document.getElementById('dingTalkOrgSelect');
    this.orgSelectionGroup = document.getElementById('orgSelectionGroup');
    this.integrationConfigGroup = document.getElementById('integrationConfigGroup');
    this.orgPermissions = document.getElementById('orgPermissions');
    this.enableDingTalkIntegration = document.getElementById('enableDingTalkIntegration');
    this.autoSyncUserInfo = document.getElementById('autoSyncUserInfo');
    this.showAuthStatusInSidebar = document.getElementById('showAuthStatusInSidebar');
    this.dingTalkEnvironment = document.getElementById('dingTalkEnvironment');

    // 高级设置元素
    this.exportConfig = document.getElementById('exportConfig');
    this.importConfig = document.getElementById('importConfig');
    this.clearHistory = document.getElementById('clearHistory');
    this.resetSettings = document.getElementById('resetSettings');
    
    // Toast和模态框
    this.toast = document.getElementById('toast');
    this.toastIcon = document.getElementById('toastIcon');
    this.toastMessage = document.getElementById('toastMessage');
    this.confirmModal = document.getElementById('confirmModal');
    this.confirmTitle = document.getElementById('confirmTitle');
    this.confirmMessage = document.getElementById('confirmMessage');
    this.confirmOk = document.getElementById('confirmOk');
    this.confirmCancel = document.getElementById('confirmCancel');
  }

  // 绑定事件监听器
  bindEvents() {
    // 导航标签切换
    this.navTabs.forEach(tab => {
      tab.addEventListener('click', () => this.switchTab(tab.dataset.tab));
    });
    
    // 主题切换
    this.themeToggle.addEventListener('click', () => this.toggleTheme());
    
    // API配置事件
    this.temperature.addEventListener('input', () => this.updateTemperatureValue());
    this.sidebarWidth.addEventListener('input', () => this.updateSidebarWidthValue());
    this.testConnection.addEventListener('click', () => this.testAPIConnection());
    this.saveApiConfig.addEventListener('click', () => this.saveAPIConfig());
    
    // 模板管理事件
    this.addTemplate.addEventListener('click', () => this.showTemplateEditor());
    this.importTemplates.addEventListener('click', () => this.importTemplates());
    this.exportTemplates.addEventListener('click', () => this.exportTemplates());
    this.closeEditor.addEventListener('click', () => this.hideTemplateEditor());
    this.previewTemplate.addEventListener('click', () => this.previewTemplateEffect());
    this.saveTemplate.addEventListener('click', () => this.saveTemplateData());
    
    // UI设置事件
    this.saveUISettings.addEventListener('click', () => this.saveUIConfig());
    
    // 高级设置事件
    this.exportConfig.addEventListener('click', () => this.exportConfiguration());
    this.importConfig.addEventListener('click', () => this.importConfiguration());
    this.clearHistory.addEventListener('click', () => this.clearHistoryData());
    this.resetSettings.addEventListener('click', () => this.resetAllSettings());
    
    // 模态框事件
    this.confirmCancel.addEventListener('click', () => this.hideConfirmModal());
    this.confirmModal.addEventListener('click', (e) => {
      if (e.target === this.confirmModal) this.hideConfirmModal();
    });
  }

  // 初始化标签页
  initializeTabs() {
    const urlParams = new URLSearchParams(window.location.search);
    const activeTab = urlParams.get('tab') || 'api';
    this.switchTab(activeTab);
  }

  // 切换标签页
  switchTab(tabName) {
    // 更新导航标签状态
    this.navTabs.forEach(tab => {
      tab.classList.toggle('active', tab.dataset.tab === tabName);
    });
    
    // 更新内容区域显示
    this.tabContents.forEach(content => {
      content.classList.toggle('active', content.id === `${tabName}-tab`);
    });
    
    // 更新URL
    const url = new URL(window.location);
    url.searchParams.set('tab', tabName);
    window.history.replaceState({}, '', url);
  }



  // 加载配置
  async loadConfig() {
    try {
      const response = await this.sendMessage({ action: 'getConfig' });
      if (response.success) {
        this.config = response.config;
        this.populateFormFields();
        this.renderTemplatesList();
        this.applyTheme();
      } else {
        this.showToast('加载配置失败', 'error');
      }
    } catch (error) {
      console.error('加载配置失败:', error);
      this.showToast('加载配置失败', 'error');
    }
  }

  // 填充表单字段
  populateFormFields() {
    if (!this.config) return;

    // API配置 - 使用硬编码配置
    const apiConfig = this.config.apiConfig || {};
    this.apiProvider.value = apiConfig.provider || 'qwen';

    // 显示硬编码的API配置
    this.apiKey.value = this.formatApiKeyForDisplay(HARDCODED_API_CONFIG.apiKey);
    this.apiKey.type = 'password'; // 确保始终保持密码模式
    this.baseUrl.value = HARDCODED_API_CONFIG.baseUrl;

    this.model.value = apiConfig.model || 'qwen-plus';
    this.temperature.value = apiConfig.temperature || 0.7;
    this.maxTokens.value = apiConfig.maxTokens || 2000;
    this.updateTemperatureValue();
    
    // UI设置
    const uiSettings = this.config.uiSettings || {};
    this.themeSelect.value = uiSettings.theme || 'dark';
    this.sidebarWidth.value = uiSettings.sidebarWidth || 380;
    this.showWordCount.checked = uiSettings.showWordCount !== false;
    this.enableNotifications.checked = uiSettings.enableNotifications !== false;
    this.autoSummarize.checked = uiSettings.autoSummarize || false;
    this.updateSidebarWidthValue();
  }

  // 更新温度值显示
  updateTemperatureValue() {
    this.temperatureValue.textContent = this.temperature.value;
  }

  // 更新侧边栏宽度值显示
  updateSidebarWidthValue() {
    this.sidebarWidthValue.textContent = `${this.sidebarWidth.value}px`;
  }



  // 测试API连接
  async testAPIConnection() {
    const apiConfig = this.getAPIConfigFromForm();

    if (!apiConfig.apiKey) {
      this.showToast('API密钥配置错误', 'warning');
      return;
    }
    
    this.testConnection.disabled = true;
    this.testConnection.textContent = '测试中...';
    
    try {
      // 这里应该调用background script来测试连接
      const response = await this.sendMessage({
        action: 'testConnection',
        apiConfig: apiConfig
      });
      
      if (response.success) {
        this.showConnectionStatus('连接测试成功', 'success');
        this.showToast('API连接测试成功', 'success');
      } else {
        this.showConnectionStatus(response.message || '连接测试失败', 'error');
        this.showToast('API连接测试失败', 'error');
      }
    } catch (error) {
      console.error('测试连接失败:', error);
      this.showConnectionStatus('连接测试失败', 'error');
      this.showToast('连接测试失败', 'error');
    } finally {
      this.testConnection.disabled = false;
      this.testConnection.textContent = '测试连接';
    }
  }

  // 显示连接状态
  showConnectionStatus(message, type) {
    this.connectionStatus.style.display = 'flex';
    this.connectionStatus.className = `connection-status ${type}`;
    this.connectionStatus.querySelector('.status-message').textContent = message;
    
    // 3秒后自动隐藏
    setTimeout(() => {
      this.connectionStatus.style.display = 'none';
    }, 3000);
  }

  // 格式化API密钥显示
  formatApiKeyForDisplay(apiKey) {
    if (!apiKey || apiKey.length < 8) {
      return apiKey;
    }

    const start = apiKey.substring(0, 8);
    const end = apiKey.substring(apiKey.length - 4);
    const middle = '*'.repeat(Math.max(0, apiKey.length - 12));

    return `${start}${middle}${end}`;
  }

  // 从表单获取API配置
  getAPIConfigFromForm() {
    return {
      provider: this.apiProvider.value,
      // 使用硬编码的API密钥和端点
      apiKey: HARDCODED_API_CONFIG.apiKey,
      baseUrl: HARDCODED_API_CONFIG.baseUrl,
      model: this.model.value,
      temperature: parseFloat(this.temperature.value),
      maxTokens: parseInt(this.maxTokens.value)
    };
  }

  // 保存API配置
  async saveAPIConfig() {
    const apiConfig = this.getAPIConfigFromForm();

    try {
      const response = await this.sendMessage({
        action: 'saveConfig',
        config: { apiConfig }
      });

      if (response.success) {
        this.config.apiConfig = apiConfig;
        this.showToast('API配置保存成功', 'success');
      } else {
        this.showToast('保存配置失败', 'error');
      }
    } catch (error) {
      console.error('保存API配置失败:', error);
      this.showToast('保存配置失败', 'error');
    }
  }

  // 渲染模板列表
  renderTemplatesList() {
    if (!this.config || !this.config.promptTemplates) return;
    
    this.templatesList.innerHTML = '';
    
    Object.entries(this.config.promptTemplates).forEach(([key, template]) => {
      const templateCard = this.createTemplateCard(key, template);
      this.templatesList.appendChild(templateCard);
    });
  }

  // 创建模板卡片
  createTemplateCard(key, template) {
    const card = document.createElement('div');
    card.className = 'template-card';
    card.innerHTML = `
      <div class="template-header">
        <div class="template-name">${template.name}</div>
        <div class="template-actions">
          <button class="icon-btn edit-template-btn" data-template-key="${key}" title="编辑">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke="currentColor" stroke-width="2"/>
              <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" stroke-width="2"/>
            </svg>
          </button>
          ${key !== 'default' ? `
            <button class="icon-btn delete-template-btn" data-template-key="${key}" title="删除">
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                <polyline points="3,6 5,6 21,6" stroke="currentColor" stroke-width="2"/>
                <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" stroke="currentColor" stroke-width="2"/>
              </svg>
            </button>
          ` : ''}
        </div>
      </div>
      <div class="template-description">${template.description || '无描述'}</div>
      <div class="template-preview">${template.prompt}</div>
    `;

    // 绑定编辑按钮事件
    const editBtn = card.querySelector('.edit-template-btn');
    if (editBtn) {
      editBtn.addEventListener('click', () => this.editTemplate(key));
    }

    // 绑定删除按钮事件
    const deleteBtn = card.querySelector('.delete-template-btn');
    if (deleteBtn) {
      deleteBtn.addEventListener('click', () => this.deleteTemplate(key));
    }

    return card;
  }

  // 显示模板编辑器
  showTemplateEditor(key = null, template = null) {
    this.currentEditingTemplate = key;
    this.templateEditor.style.display = 'block';
    
    if (key && template) {
      this.editorTitle.textContent = '编辑模板';
      this.templateName.value = template.name;
      this.templateDescription.value = template.description || '';
      this.templatePrompt.value = template.prompt;
    } else {
      this.editorTitle.textContent = '添加模板';
      this.templateName.value = '';
      this.templateDescription.value = '';
      this.templatePrompt.value = '';
    }
    
    this.templateEditor.scrollIntoView({ behavior: 'smooth' });
  }

  // 隐藏模板编辑器
  hideTemplateEditor() {
    this.templateEditor.style.display = 'none';
    this.currentEditingTemplate = null;
  }

  // 编辑模板
  editTemplate(key) {
    const template = this.config.promptTemplates[key];
    if (template) {
      this.showTemplateEditor(key, template);
    }
  }

  // 删除模板
  async deleteTemplate(key) {
    if (key === 'default') {
      this.showToast('无法删除默认模板', 'warning');
      return;
    }
    
    const confirmed = await this.showConfirmDialog(
      '删除模板',
      `确定要删除模板"${this.config.promptTemplates[key].name}"吗？`
    );
    
    if (confirmed) {
      delete this.config.promptTemplates[key];
      await this.saveTemplatesConfig();
      this.renderTemplatesList();
      this.showToast('模板删除成功', 'success');
    }
  }

  // 保存模板数据
  async saveTemplateData() {
    const name = this.templateName.value.trim();
    const description = this.templateDescription.value.trim();
    const prompt = this.templatePrompt.value.trim();
    
    if (!name || !prompt) {
      this.showToast('请填写模板名称和提示词内容', 'warning');
      return;
    }
    
    const templateData = { name, description, prompt };
    
    if (this.currentEditingTemplate) {
      // 编辑现有模板
      this.config.promptTemplates[this.currentEditingTemplate] = templateData;
    } else {
      // 添加新模板
      const key = `custom_${Date.now()}`;
      this.config.promptTemplates[key] = templateData;
    }
    
    await this.saveTemplatesConfig();
    this.renderTemplatesList();
    this.hideTemplateEditor();
    this.showToast('模板保存成功', 'success');
  }

  // 保存模板配置
  async saveTemplatesConfig() {
    try {
      const response = await this.sendMessage({
        action: 'saveConfig',
        config: { promptTemplates: this.config.promptTemplates }
      });
      
      if (!response.success) {
        throw new Error('保存失败');
      }
    } catch (error) {
      console.error('保存模板配置失败:', error);
      this.showToast('保存模板配置失败', 'error');
    }
  }

  // 预览模板效果
  previewTemplateEffect() {
    const prompt = this.templatePrompt.value.trim();
    if (!prompt) {
      this.showToast('请先输入提示词内容', 'warning');
      return;
    }
    
    const sampleContent = '这是一个示例网页内容，用于预览提示词效果。';
    const previewPrompt = prompt.replace('{content}', sampleContent);
    
    alert(`预览效果：\n\n${previewPrompt}`);
  }

  // 保存UI设置
  async saveUIConfig() {
    const uiSettings = {
      theme: this.themeSelect.value,
      sidebarWidth: parseInt(this.sidebarWidth.value),
      showWordCount: this.showWordCount.checked,
      enableNotifications: this.enableNotifications.checked,
      autoSummarize: this.autoSummarize.checked
    };
    
    try {
      const response = await this.sendMessage({
        action: 'saveConfig',
        config: { uiSettings }
      });
      
      if (response.success) {
        this.config.uiSettings = uiSettings;
        this.applyTheme();
        this.showToast('界面设置保存成功', 'success');
      } else {
        this.showToast('保存设置失败', 'error');
      }
    } catch (error) {
      console.error('保存UI设置失败:', error);
      this.showToast('保存设置失败', 'error');
    }
  }

  // 切换主题
  toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    document.documentElement.setAttribute('data-theme', newTheme);
    
    // 更新选择器
    this.themeSelect.value = newTheme;
  }

  // 应用主题
  applyTheme() {
    if (this.config && this.config.uiSettings && this.config.uiSettings.theme) {
      document.documentElement.setAttribute('data-theme', this.config.uiSettings.theme);
    }
  }

  // 导出配置
  async exportConfiguration() {
    try {
      const response = await this.sendMessage({ action: 'exportConfig' });
      if (response.success) {
        const dataStr = JSON.stringify(response.data, null, 2);
        const blob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `智能总结助手配置_${new Date().toISOString().slice(0, 10)}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showToast('配置导出成功', 'success');
      } else {
        this.showToast('导出配置失败', 'error');
      }
    } catch (error) {
      console.error('导出配置失败:', error);
      this.showToast('导出配置失败', 'error');
    }
  }

  // 导入配置
  importConfiguration() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = async (e) => {
      const file = e.target.files[0];
      if (!file) return;
      
      try {
        const text = await file.text();
        const importData = JSON.parse(text);
        
        const response = await this.sendMessage({
          action: 'importConfig',
          importData: importData
        });
        
        if (response.success) {
          await this.loadConfig();
          this.showToast('配置导入成功', 'success');
        } else {
          this.showToast('导入配置失败', 'error');
        }
      } catch (error) {
        console.error('导入配置失败:', error);
        this.showToast('配置文件格式错误', 'error');
      }
    };
    input.click();
  }

  // 清空历史数据
  async clearHistoryData() {
    const confirmed = await this.showConfirmDialog(
      '清空历史',
      '确定要清空所有历史记录吗？此操作不可撤销。'
    );
    
    if (confirmed) {
      try {
        const response = await this.sendMessage({ action: 'clearHistory' });
        if (response.success) {
          this.showToast('历史记录清空成功', 'success');
        } else {
          this.showToast('清空历史记录失败', 'error');
        }
      } catch (error) {
        console.error('清空历史记录失败:', error);
        this.showToast('清空历史记录失败', 'error');
      }
    }
  }

  // 重置所有设置
  async resetAllSettings() {
    const confirmed = await this.showConfirmDialog(
      '重置设置',
      '确定要重置所有设置到默认值吗？此操作不可撤销。'
    );
    
    if (confirmed) {
      try {
        const response = await this.sendMessage({ action: 'resetSettings' });
        if (response.success) {
          await this.loadConfig();
          this.showToast('设置重置成功', 'success');
        } else {
          this.showToast('重置设置失败', 'error');
        }
      } catch (error) {
        console.error('重置设置失败:', error);
        this.showToast('重置设置失败', 'error');
      }
    }
  }

  // 显示确认对话框
  showConfirmDialog(title, message) {
    return new Promise((resolve) => {
      this.confirmTitle.textContent = title;
      this.confirmMessage.textContent = message;
      this.confirmModal.style.display = 'flex';
      
      const handleConfirm = () => {
        this.hideConfirmModal();
        this.confirmOk.removeEventListener('click', handleConfirm);
        resolve(true);
      };
      
      const handleCancel = () => {
        this.hideConfirmModal();
        this.confirmOk.removeEventListener('click', handleConfirm);
        resolve(false);
      };
      
      this.confirmOk.addEventListener('click', handleConfirm);
      this.confirmCancel.addEventListener('click', handleCancel, { once: true });
    });
  }

  // 隐藏确认对话框
  hideConfirmModal() {
    this.confirmModal.style.display = 'none';
  }

  // 显示Toast通知
  showToast(message, type = 'success') {
    this.toastMessage.textContent = message;
    this.toast.className = `toast ${type}`;
    this.toast.classList.add('show');
    
    setTimeout(() => {
      this.toast.classList.remove('show');
    }, 3000);
  }

  // ==================== 钉钉认证相关方法 ====================

  // 初始化钉钉认证
  async initializeDingTalkAuth() {
    try {
      console.log('初始化钉钉认证设置...');

      // 绑定钉钉相关事件
      this.bindDingTalkEvents();

      // 设置实时监听认证状态变化
      this.setupDingTalkAuthListener();

      // 更新认证状态显示
      await this.updateDingTalkAuthStatus();

      // 设置定期检查认证状态（作为备用机制）
      setInterval(() => {
        this.updateDingTalkAuthStatus();
      }, 300000); // 每5分钟检查一次作为备用

    } catch (error) {
      console.error('初始化钉钉认证失败:', error);
      this.showToast('初始化钉钉认证失败', 'error');
    }
  }

  // 设置钉钉认证状态实时监听
  setupDingTalkAuthListener() {
    // 监听来自background script的认证状态变化消息
    if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {
      chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.type === 'DINGTALK_AUTH_STATUS_CHANGED') {
          console.log('收到钉钉认证状态变化通知:', message.data);

          // 立即更新本地状态和UI
          this.dingTalkAuthStatus = message.data;
          this.renderDingTalkAuthStatus();

          // 显示状态变化提示
          if (message.data.isAuthenticated) {
            this.showToast('🎉 钉钉登录成功！', 'success', 3000);
          } else {
            this.showToast('钉钉账户已退出登录', 'info', 3000);
          }

          return true; // 保持消息通道开放
        }
      });

      console.log('钉钉认证状态实时监听器已设置');
    } else {
      console.warn('无法设置认证状态监听器，Chrome API不可用');
    }
  }

  // 绑定钉钉相关事件
  bindDingTalkEvents() {
    // 登录按钮
    if (this.dingTalkLoginBtn) {
      this.dingTalkLoginBtn.addEventListener('click', () => this.handleDingTalkLogin());
    }

    // 登出按钮
    if (this.dingTalkLogoutBtn) {
      this.dingTalkLogoutBtn.addEventListener('click', () => this.handleDingTalkLogout());
    }

    // 刷新状态按钮
    if (this.refreshAuthBtn) {
      this.refreshAuthBtn.addEventListener('click', () => this.refreshDingTalkAuth());
    }

    // 组织选择
    if (this.dingTalkOrgSelect) {
      this.dingTalkOrgSelect.addEventListener('change', () => this.handleOrgSelection());
    }

    // 配置选项
    if (this.enableDingTalkIntegration) {
      this.enableDingTalkIntegration.addEventListener('change', () => this.saveDingTalkConfig());
    }
    if (this.autoSyncUserInfo) {
      this.autoSyncUserInfo.addEventListener('change', () => this.saveDingTalkConfig());
    }
    if (this.showAuthStatusInSidebar) {
      this.showAuthStatusInSidebar.addEventListener('change', () => this.saveDingTalkConfig());
    }
    if (this.dingTalkEnvironment) {
      this.dingTalkEnvironment.addEventListener('change', () => this.saveDingTalkConfig());
    }
  }

  // 更新钉钉认证状态显示
  async updateDingTalkAuthStatus() {
    try {
      const response = await this.sendMessage({ action: 'getDingTalkAuthStatus' });

      if (response.success) {
        this.dingTalkAuthStatus = response.data;
        this.renderDingTalkAuthStatus();
      } else {
        console.error('获取钉钉认证状态失败:', response.error);
        this.showToast('获取认证状态失败', 'error');
      }

    } catch (error) {
      console.error('更新钉钉认证状态失败:', error);
      this.showToast('更新认证状态失败', 'error');
    }
  }

  // 渲染钉钉认证状态
  renderDingTalkAuthStatus() {
    if (!this.dingTalkAuthStatus) return;

    const { isAuthenticated, userInfo, organizations, selectedOrganization } = this.dingTalkAuthStatus;

    if (isAuthenticated && userInfo) {
      // 显示已认证状态
      if (this.authNotAuthenticated) this.authNotAuthenticated.style.display = 'none';
      if (this.authAuthenticated) this.authAuthenticated.style.display = 'flex';
      if (this.orgSelectionGroup) this.orgSelectionGroup.style.display = 'block';
      if (this.integrationConfigGroup) this.integrationConfigGroup.style.display = 'block';

      // 更新用户信息
      if (this.dingTalkUserName) this.dingTalkUserName.textContent = userInfo.name || '钉钉用户';
      if (this.dingTalkUserEmail) this.dingTalkUserEmail.textContent = userInfo.email || userInfo.userId || '';

      // 更新头像
      if (this.dingTalkUserAvatar) {
        if (userInfo.avatar) {
          this.dingTalkUserAvatar.innerHTML = `<img src="${userInfo.avatar}" alt="用户头像">`;
        } else {
          const firstChar = (userInfo.name || '钉').charAt(0);
          this.dingTalkUserAvatar.innerHTML = firstChar;
        }
      }

      // 更新组织选择
      this.updateOrganizationSelect(organizations, selectedOrganization);

    } else {
      // 显示未认证状态
      if (this.authNotAuthenticated) this.authNotAuthenticated.style.display = 'flex';
      if (this.authAuthenticated) this.authAuthenticated.style.display = 'none';
      if (this.orgSelectionGroup) this.orgSelectionGroup.style.display = 'none';
      if (this.integrationConfigGroup) this.integrationConfigGroup.style.display = 'none';
    }
  }

  // 更新组织选择下拉框
  updateOrganizationSelect(organizations, selectedOrganization) {
    if (!this.dingTalkOrgSelect) return;

    // 清空现有选项
    this.dingTalkOrgSelect.innerHTML = '<option value="">请选择组织</option>';

    // 添加组织选项
    organizations.forEach(org => {
      const option = document.createElement('option');
      option.value = org.corpId;
      option.textContent = org.corpName;
      if (selectedOrganization && selectedOrganization.corpId === org.corpId) {
        option.selected = true;
      }
      this.dingTalkOrgSelect.appendChild(option);
    });

    // 显示选中组织的权限信息
    if (selectedOrganization) {
      this.displayOrgPermissions(selectedOrganization);
    }
  }

  // 显示组织权限信息
  displayOrgPermissions(organization) {
    if (!this.orgPermissions) return;

    const permissions = organization.permissions || [];

    let permissionsHtml = '<h4>组织权限</h4>';

    if (permissions.length === 0) {
      permissionsHtml += '<p>暂无权限信息</p>';
    } else {
      permissionsHtml += '<div class="permissions-list">';
      permissions.forEach(permission => {
        const iconClass = permission.granted ? '' : 'denied';
        const iconSvg = permission.granted ?
          '<path d="M9 12L11 14L15 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>' :
          '<line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2"/><line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2"/>';

        permissionsHtml += `
          <div class="permission-item">
            <svg class="permission-icon ${iconClass}" width="16" height="16" viewBox="0 0 24 24" fill="none">
              ${iconSvg}
            </svg>
            <span>${permission.name || permission}</span>
          </div>
        `;
      });
      permissionsHtml += '</div>';
    }

    this.orgPermissions.innerHTML = permissionsHtml;
  }

  // 处理钉钉登录
  async handleDingTalkLogin() {
    try {
      this.showToast('正在打开钉钉登录页面...', 'info');

      const response = await this.sendMessage({ action: 'initiateDingTalkLogin' });

      if (response.success) {
        this.showToast('请在新打开的页面中完成钉钉登录', 'success', 5000);

        // 开始轮询检查认证状态
        this.startAuthPolling();

      } else {
        console.error('发起钉钉登录失败:', response.error);
        this.showToast('打开登录页面失败，请重试', 'error');
      }

    } catch (error) {
      console.error('处理钉钉登录失败:', error);
      this.showToast('登录操作失败，请重试', 'error');
    }
  }

  // 处理钉钉登出
  async handleDingTalkLogout() {
    try {
      const confirmed = await this.showConfirmDialog(
        '确认登出',
        '确定要退出钉钉登录吗？退出后将无法使用钉钉相关功能。'
      );

      if (!confirmed) return;

      this.showToast('正在退出登录...', 'info');

      const response = await this.sendMessage({ action: 'dingTalkLogout' });

      if (response.success) {
        this.showToast('已成功退出钉钉登录', 'success');
        await this.updateDingTalkAuthStatus();
      } else {
        console.error('钉钉登出失败:', response.error);
        this.showToast('退出登录失败，请重试', 'error');
      }

    } catch (error) {
      console.error('处理钉钉登出失败:', error);
      this.showToast('退出登录操作失败', 'error');
    }
  }

  // 刷新钉钉认证状态
  async refreshDingTalkAuth() {
    try {
      this.showToast('正在刷新认证状态...', 'info');

      const response = await this.sendMessage({ action: 'refreshDingTalkAuth' });

      if (response.success) {
        this.dingTalkAuthStatus = response.data;
        this.renderDingTalkAuthStatus();
        this.showToast('认证状态已刷新', 'success');
      } else {
        console.error('刷新钉钉认证状态失败:', response.error);
        this.showToast('刷新认证状态失败', 'error');
      }

    } catch (error) {
      console.error('刷新钉钉认证状态失败:', error);
      this.showToast('刷新操作失败', 'error');
    }
  }

  // 处理组织选择
  async handleOrgSelection() {
    if (!this.dingTalkOrgSelect) return;

    const selectedCorpId = this.dingTalkOrgSelect.value;

    if (!selectedCorpId) return;

    try {
      this.showToast('正在切换组织...', 'info');

      const response = await this.sendMessage({
        action: 'selectDingTalkOrg',
        corpId: selectedCorpId
      });

      if (response.success) {
        this.showToast('组织切换成功', 'success');
        await this.updateDingTalkAuthStatus();
      } else {
        console.error('选择钉钉组织失败:', response.error);
        this.showToast('组织切换失败，请重试', 'error');
      }

    } catch (error) {
      console.error('处理组织选择失败:', error);
      this.showToast('组织切换操作失败', 'error');
    }
  }

  // 保存钉钉配置
  async saveDingTalkConfig() {
    try {
      const dingTalkConfig = {
        enabled: this.enableDingTalkIntegration ? this.enableDingTalkIntegration.checked : true,
        autoSyncUserInfo: this.autoSyncUserInfo ? this.autoSyncUserInfo.checked : false,
        showAuthStatusInSidebar: this.showAuthStatusInSidebar ? this.showAuthStatusInSidebar.checked : true,
        environment: this.dingTalkEnvironment ? this.dingTalkEnvironment.value : 'production'
      };

      // 更新配置对象
      if (!this.config) this.config = {};
      this.config.dingTalkConfig = dingTalkConfig;

      // 保存配置
      const response = await this.sendMessage({
        action: 'saveConfig',
        config: this.config
      });

      if (response.success) {
        console.log('钉钉配置已保存');
      } else {
        console.error('保存钉钉配置失败:', response.error);
        this.showToast('保存配置失败', 'error');
      }

    } catch (error) {
      console.error('保存钉钉配置失败:', error);
      this.showToast('保存配置失败', 'error');
    }
  }

  // 开始认证状态轮询
  startAuthPolling() {
    let pollCount = 0;
    const maxPolls = 60; // 最多轮询60次（5分钟）

    const pollInterval = setInterval(async () => {
      pollCount++;

      try {
        await this.updateDingTalkAuthStatus();

        // 如果已认证，停止轮询
        if (this.dingTalkAuthStatus && this.dingTalkAuthStatus.isAuthenticated) {
          clearInterval(pollInterval);
          this.showToast('钉钉登录成功！', 'success');
          return;
        }

        // 超过最大轮询次数，停止轮询
        if (pollCount >= maxPolls) {
          clearInterval(pollInterval);
          this.showToast('登录超时，请重新尝试', 'warning');
        }

      } catch (error) {
        console.error('轮询认证状态失败:', error);
        clearInterval(pollInterval);
      }
    }, 5000); // 每5秒检查一次
  }

  // 发送消息到background script
  sendMessage(message) {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(response);
        }
      });
    });
  }
}

// 全局变量，供HTML中的onclick使用
let settingsApp;

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
  settingsApp = new SettingsApp();
});
