/* 智能网页总结助手 - 设置页面样式 */

:root {
  /* 深色主题色彩 */
  --bg-primary: #0f0f23;
  --bg-secondary: #1a1a2e;
  --bg-tertiary: #16213e;
  --bg-card: #1e1e3f;
  --bg-hover: #2a2a4a;
  --bg-input: #252545;
  
  /* 渐变色 */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --gradient-success: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
  --gradient-warning: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-danger: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
  
  /* 文字颜色 */
  --text-primary: #ffffff;
  --text-secondary: #b8b8d1;
  --text-muted: #8b8ba7;
  --text-accent: #64ffda;
  --text-success: #4caf50;
  --text-warning: #ff9800;
  --text-danger: #f44336;
  
  /* 边框和分割线 */
  --border-color: #2a2a4a;
  --border-radius: 12px;
  --border-radius-sm: 8px;
  --border-radius-lg: 16px;
  
  /* 阴影 */
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.15);
  --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.3);
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  
  /* 动画 */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* 浅色主题 */
[data-theme="light"] {
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #e9ecef;
  --bg-card: #ffffff;
  --bg-hover: #f1f3f4;
  --bg-input: #ffffff;
  
  --text-primary: #1a1a1a;
  --text-secondary: #4a4a4a;
  --text-muted: #6c757d;
  --text-accent: #0066cc;
  
  --border-color: #dee2e6;
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.08);
  --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.12);
  --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.16);
}

/* 基础样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  min-height: 100vh;
}

/* 设置容器 */
.settings-container {
  max-width: 1200px;
  margin: 0 auto;
  background: var(--bg-primary);
  min-height: 100vh;
}

/* 头部区域 */
.settings-header {
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-lg) var(--spacing-xl);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.logo-icon {
  width: 48px;
  height: 48px;
  background: var(--gradient-primary);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.logo-text h1 {
  font-size: 24px;
  font-weight: 700;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--spacing-xs);
}

.logo-text p {
  font-size: 14px;
  color: var(--text-muted);
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* 图标按钮 */
.icon-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: var(--bg-card);
  color: var(--text-secondary);
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  border: 1px solid var(--border-color);
}

.icon-btn:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

/* 导航标签 */
.settings-nav {
  display: flex;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: 0 var(--spacing-xl);
  overflow-x: auto;
}

.nav-tab {
  background: none;
  border: none;
  padding: var(--spacing-md) var(--spacing-lg);
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  border-bottom: 2px solid transparent;
  white-space: nowrap;
}

.nav-tab:hover {
  color: var(--text-primary);
  background: var(--bg-hover);
}

.nav-tab.active {
  color: var(--text-accent);
  border-bottom-color: var(--text-accent);
}

/* 主要内容区域 */
.settings-main {
  padding: var(--spacing-xl);
}

.tab-content {
  display: none;
  max-width: 800px;
  margin: 0 auto;
}

.tab-content.active {
  display: block;
  animation: fadeIn var(--transition-normal);
}

/* 区块头部 */
.section-header {
  margin-bottom: var(--spacing-2xl);
  text-align: center;
}

.section-header h2 {
  font-size: 28px;
  font-weight: 700;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--spacing-sm);
}

.section-header p {
  font-size: 16px;
  color: var(--text-secondary);
}

/* 表单样式 */
.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-input);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  color: var(--text-primary);
  font-size: 14px;
  transition: all var(--transition-fast);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--text-accent);
  box-shadow: 0 0 0 2px rgba(100, 255, 218, 0.2);
}

/* 只读输入框样式 */
.form-input[readonly] {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  cursor: not-allowed;
  border-style: dashed;
  opacity: 0.8;
}

.form-input[readonly]:focus {
  border-color: var(--border-color);
  box-shadow: none;
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.form-help {
  display: block;
  font-size: 12px;
  color: var(--text-muted);
  margin-top: var(--spacing-xs);
}

/* 配置信息样式 */
.config-info {
  display: inline-flex;
  align-items: center;
  color: var(--text-accent);
  font-weight: 500;
}

.config-info svg {
  flex-shrink: 0;
}

/* 输入组 */
.input-group {
  display: flex;
  align-items: center;
}

.input-group .form-input {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
}

.input-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-left: none;
  border-top-right-radius: var(--border-radius-sm);
  border-bottom-right-radius: var(--border-radius-sm);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.input-btn:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

/* 表单行 */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
}

/* 范围滑块 */
.form-range {
  width: 100%;
  height: 6px;
  background: var(--bg-tertiary);
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
}

.form-range::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 20px;
  height: 20px;
  background: var(--gradient-accent);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: var(--shadow-sm);
}

.form-range::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: var(--gradient-accent);
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: var(--shadow-sm);
}

.range-value {
  text-align: center;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-accent);
  margin-top: var(--spacing-xs);
}

/* 复选框 */
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  font-size: 14px;
  color: var(--text-primary);
}

.form-checkbox {
  display: none;
}

.checkbox-custom {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  background: var(--bg-input);
  position: relative;
  transition: all var(--transition-fast);
}

.form-checkbox:checked + .checkbox-custom {
  background: var(--gradient-accent);
  border-color: transparent;
}

.form-checkbox:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* 按钮样式 */
.btn {
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  border-radius: var(--border-radius-sm);
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--bg-card);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background: var(--bg-hover);
  border-color: var(--text-accent);
}

.btn-danger {
  background: var(--gradient-danger);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* 表单操作区域 */
.form-actions {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xl);
  flex-wrap: wrap;
}

/* 连接状态 */
.connection-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-sm);
  margin-top: var(--spacing-md);
  animation: slideIn var(--transition-normal);
}

.connection-status.success {
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid var(--text-success);
  color: var(--text-success);
}

.connection-status.error {
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid var(--text-danger);
  color: var(--text-danger);
}

.status-icon {
  width: 16px;
  height: 16px;
  border-radius: 50%;
}

.connection-status.success .status-icon {
  background: var(--text-success);
}

.connection-status.error .status-icon {
  background: var(--text-danger);
}

/* 模板相关样式 */
.templates-toolbar {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  flex-wrap: wrap;
}

.templates-list {
  display: grid;
  gap: var(--spacing-md);
}

.template-card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  transition: all var(--transition-fast);
}

.template-card:hover {
  border-color: var(--text-accent);
  box-shadow: var(--shadow-sm);
}

.template-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
}

.template-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.template-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.template-description {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.template-preview {
  font-size: 12px;
  color: var(--text-muted);
  background: var(--bg-tertiary);
  padding: var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  max-height: 60px;
  overflow: hidden;
  position: relative;
}

.template-preview::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(transparent, var(--bg-tertiary));
}

/* 模板编辑器 */
.template-editor {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  margin-top: var(--spacing-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.editor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-color);
}

.editor-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.editor-content {
  padding: var(--spacing-lg);
}

.editor-actions {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

/* 设置组 */
.settings-group {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.settings-group h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

/* 钉钉集成样式 */
.dingtalk-auth-display {
  margin-bottom: var(--spacing-lg);
}

.auth-status-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius);
  border: 2px solid;
  background: var(--bg-card);
}

.auth-not-authenticated {
  border-color: #ff4d4f;
  background: #fff2f0;
}

.auth-authenticated {
  border-color: #52c41a;
  background: #f6ffed;
}

.auth-status-icon {
  flex-shrink: 0;
}

.auth-status-content {
  flex: 1;
}

.auth-status-content h4 {
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 600;
}

.auth-status-content p {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-secondary);
  font-size: 14px;
}

.auth-user-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-md);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.user-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  font-weight: 600;
  overflow: hidden;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.user-email {
  font-size: 14px;
  color: var(--text-secondary);
}

.auth-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.org-permissions {
  margin-top: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.permission-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-xs);
  font-size: 14px;
}

.permission-item:last-child {
  margin-bottom: 0;
}

.permission-icon {
  width: 16px;
  height: 16px;
  color: #52c41a;
}

.permission-icon.denied {
  color: #ff4d4f;
}

/* 深色主题下的钉钉集成样式 */
[data-theme="dark"] .auth-not-authenticated {
  background: rgba(255, 77, 79, 0.1);
  border-color: #ff4d4f;
}

[data-theme="dark"] .auth-authenticated {
  background: rgba(82, 196, 26, 0.1);
  border-color: #52c41a;
}

.about-info {
  color: var(--text-secondary);
  line-height: 1.8;
}

.about-info p {
  margin-bottom: var(--spacing-sm);
}

/* Toast 通知 */
.toast {
  position: fixed;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  transform: translateX(100%);
  transition: transform var(--transition-normal);
  min-width: 300px;
}

.toast.show {
  transform: translateX(0);
}

.toast-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.toast-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
}

.toast.success .toast-icon {
  background: var(--text-success);
}

.toast.error .toast-icon {
  background: var(--text-danger);
}

.toast.warning .toast-icon {
  background: var(--text-warning);
}

.toast-message {
  font-size: 14px;
  color: var(--text-primary);
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(4px);
}

.modal {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  animation: modalSlideIn var(--transition-normal);
}

.modal-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-tertiary);
}

.modal-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.modal-body {
  padding: var(--spacing-lg);
}

.modal-body p {
  color: var(--text-secondary);
  line-height: 1.6;
}

.modal-actions {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  background: var(--bg-tertiary);
  justify-content: flex-end;
}

/* 动画 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { opacity: 0; transform: translateX(20px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes modalSlideIn {
  from { opacity: 0; transform: scale(0.9) translateY(-20px); }
  to { opacity: 1; transform: scale(1) translateY(0); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings-header {
    padding: var(--spacing-md);
  }
  
  .settings-main {
    padding: var(--spacing-md);
  }
  
  .header-content {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }
  
  .settings-nav {
    padding: 0 var(--spacing-md);
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .templates-toolbar {
    flex-direction: column;
  }
  
  .toast {
    right: var(--spacing-md);
    left: var(--spacing-md);
    min-width: auto;
  }
}
