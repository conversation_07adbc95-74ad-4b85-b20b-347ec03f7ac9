/* Markdown预览窗口样式 */

/* CSS变量定义 */
:root {
  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --secondary-color: #64748b;
  --success-color: #059669;
  --warning-color: #d97706;
  --danger-color: #dc2626;
  
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-card: #ffffff;
  --bg-hover: #f1f5f9;
  
  --text-primary: #1e293b;
  --text-secondary: #475569;
  --text-muted: #94a3b8;
  --text-accent: #2563eb;
  
  --border-color: #e2e8f0;
  --border-radius: 8px;
  --border-radius-sm: 4px;
  
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
}

/* 深色主题 */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-card: #1e293b;
    --bg-hover: #334155;
    
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-muted: #64748b;
    --text-accent: #60a5fa;
    
    --border-color: #334155;
  }
}

/* 基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  overflow: hidden; /* 保持body隐藏溢出，让预览容器处理滚动 */
}

/* 预览容器 */
.preview-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
}

/* 工具栏 */
.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  flex-shrink: 0;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.preview-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.meta-info {
  display: flex;
  gap: var(--spacing-md);
  font-size: 12px;
  color: var(--text-muted);
}

.meta-info span {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  background: var(--bg-card);
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
}

.btn:hover {
  background: var(--bg-hover);
  border-color: var(--text-accent);
  color: var(--text-accent);
}

.btn-primary {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
  color: white;
}

.btn-secondary {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
}

/* 预览内容区域 */
.preview-content {
  flex: 1;
  padding: var(--spacing-xl);
  overflow-y: auto;
  overflow-x: hidden;
  background: var(--bg-primary);
  min-height: 0; /* 确保flex子元素可以收缩 */
  word-wrap: break-word; /* 确保长文本能够换行 */
  word-break: break-word; /* 处理长单词 */
}

/* Markdown内容样式 */
.preview-content h1,
.preview-content h2,
.preview-content h3,
.preview-content h4,
.preview-content h5,
.preview-content h6 {
  color: var(--text-primary);
  margin: var(--spacing-lg) 0 var(--spacing-md) 0;
  font-weight: 600;
  line-height: 1.3;
}

.preview-content h1 { font-size: 2.25rem; }
.preview-content h2 { font-size: 1.875rem; }
.preview-content h3 { font-size: 1.5rem; }
.preview-content h4 { font-size: 1.25rem; }
.preview-content h5 { font-size: 1.125rem; }
.preview-content h6 { font-size: 1rem; }

.preview-content h1:first-child,
.preview-content h2:first-child,
.preview-content h3:first-child {
  margin-top: 0;
}

.preview-content p {
  margin: var(--spacing-md) 0;
  line-height: 1.7;
  color: var(--text-primary);
}

.preview-content strong {
  font-weight: 600;
  color: var(--text-primary);
}

.preview-content em {
  font-style: italic;
  color: var(--text-secondary);
}

.preview-content del {
  text-decoration: line-through;
  color: var(--text-muted);
}

.preview-content code {
  background: var(--bg-secondary);
  padding: 3px 6px;
  border-radius: var(--border-radius-sm);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
  font-size: 0.875rem;
  color: var(--text-accent);
  border: 1px solid var(--border-color);
  font-weight: 500;
}

.preview-content pre {
  background: var(--bg-secondary);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius);
  overflow-x: auto;
  margin: var(--spacing-lg) 0;
  border: 1px solid var(--border-color);
  position: relative;
  line-height: 1.5;
}

.preview-content pre code {
  background: none;
  padding: 0;
  color: var(--text-primary);
  font-size: 0.875rem;
  border: none;
  font-weight: normal;
}

/* 代码块语言标识 */
.preview-content pre code.language-javascript,
.preview-content pre code.language-js {
  color: #f7df1e;
}

.preview-content pre code.language-python {
  color: #3776ab;
}

.preview-content pre code.language-css {
  color: #1572b6;
}

.preview-content pre code.language-html {
  color: #e34f26;
}

.preview-content pre code.language-json {
  color: #000000;
}

/* 代码块滚动条 */
.preview-content pre::-webkit-scrollbar {
  height: 8px;
}

.preview-content pre::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: 4px;
}

.preview-content pre::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

.preview-content pre::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

.preview-content blockquote {
  border-left: 4px solid var(--text-accent);
  padding-left: var(--spacing-md);
  margin: var(--spacing-md) 0;
  color: var(--text-secondary);
  font-style: italic;
  background: var(--bg-secondary);
  padding: var(--spacing-md);
  border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.preview-content ul,
.preview-content ol {
  margin: var(--spacing-md) 0;
  padding-left: var(--spacing-lg);
}

.preview-content li {
  margin: var(--spacing-xs) 0;
  line-height: 1.6;
}

.preview-content a {
  color: var(--text-accent);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color var(--transition-fast);
}

.preview-content a:hover {
  border-bottom-color: var(--text-accent);
}

.preview-content img {
  max-width: 100%;
  height: auto;
  border-radius: var(--border-radius);
  margin: var(--spacing-md) 0;
  box-shadow: var(--shadow-md);
}

.preview-content hr {
  border: none;
  height: 1px;
  background: var(--border-color);
  margin: var(--spacing-xl) 0;
}

/* 表格样式 */
.preview-content table,
.preview-content .markdown-table {
  width: 100%;
  border-collapse: collapse;
  margin: var(--spacing-lg) 0;
  background: var(--bg-card);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  font-size: 0.9rem;
}

.preview-content table th,
.preview-content table td,
.preview-content .markdown-table th,
.preview-content .markdown-table td {
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  text-align: left;
  vertical-align: top;
  line-height: 1.5;
}

.preview-content table th,
.preview-content .markdown-table th {
  background: var(--bg-tertiary);
  font-weight: 600;
  color: var(--text-primary);
  position: sticky;
  top: 0;
  z-index: 10;
}

.preview-content table tbody tr:nth-child(even),
.preview-content .markdown-table tr:nth-child(even) {
  background: var(--bg-secondary);
}

.preview-content table tbody tr:hover,
.preview-content .markdown-table tr:hover {
  background: var(--bg-hover);
}

/* 表格响应式处理 */
@media (max-width: 768px) {
  .preview-content table,
  .preview-content .markdown-table {
    font-size: 0.8rem;
  }

  .preview-content table th,
  .preview-content table td,
  .preview-content .markdown-table th,
  .preview-content .markdown-table td {
    padding: var(--spacing-sm);
  }
}

/* 目录样式 */
.preview-content .markdown-toc {
  background: var(--bg-secondary);
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  margin: var(--spacing-md) 0 var(--spacing-xl) 0;
  border: 1px solid var(--border-color);
}

.preview-content .markdown-toc h4 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
  font-size: 1rem;
}

.preview-content .markdown-toc ul {
  margin: 0;
  padding-left: var(--spacing-md);
}

.preview-content .markdown-toc li {
  margin: var(--spacing-xs) 0;
}

.preview-content .markdown-toc a {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.preview-content .markdown-toc a:hover {
  color: var(--text-accent);
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--spacing-md) auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 滚动条样式 */
.preview-content::-webkit-scrollbar {
  width: 8px;
}

.preview-content::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

.preview-content::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

.preview-content::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .preview-content {
    padding: var(--spacing-lg);
  }

  .toolbar {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .toolbar-left {
    gap: var(--spacing-sm);
  }

  .preview-title {
    font-size: 16px;
  }

  .meta-info {
    font-size: 11px;
    gap: var(--spacing-sm);
  }
}

@media (max-width: 600px) {
  .preview-content {
    padding: var(--spacing-md);
  }

  .toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }

  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }

  .meta-info {
    flex-wrap: wrap;
    justify-content: center;
  }

  .btn {
    padding: var(--spacing-sm);
    font-size: 12px;
  }

  .preview-content h1 { font-size: 1.75rem; }
  .preview-content h2 { font-size: 1.5rem; }
  .preview-content h3 { font-size: 1.25rem; }
  .preview-content h4 { font-size: 1.125rem; }
  .preview-content h5 { font-size: 1rem; }
  .preview-content h6 { font-size: 0.875rem; }
}

/* 打印样式 */
@media print {
  .toolbar {
    display: none;
  }

  .preview-container {
    height: auto;
  }

  .preview-content {
    padding: 0;
    overflow: visible;
    max-height: none;
  }

  .preview-content a {
    color: var(--text-primary) !important;
    text-decoration: underline;
  }

  .preview-content a:after {
    content: " (" attr(href) ")";
    font-size: 0.8em;
    color: var(--text-muted);
  }

  .preview-content img {
    max-width: 100%;
    page-break-inside: avoid;
  }

  .preview-content h1,
  .preview-content h2,
  .preview-content h3,
  .preview-content h4,
  .preview-content h5,
  .preview-content h6 {
    page-break-after: avoid;
  }

  .preview-content pre,
  .preview-content blockquote,
  .preview-content table {
    page-break-inside: avoid;
  }
}

/* 选择文本样式 */
.preview-content ::selection {
  background: rgba(37, 99, 235, 0.2);
  color: var(--text-primary);
}

.preview-content ::-moz-selection {
  background: rgba(37, 99, 235, 0.2);
  color: var(--text-primary);
}

/* 焦点样式 */
.btn:focus,
.preview-content a:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* 动画效果 */
.preview-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
