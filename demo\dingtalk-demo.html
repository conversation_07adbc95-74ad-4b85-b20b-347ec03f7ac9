<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钉钉认证功能演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .demo-container {
            background: white;
            border-radius: 16px;
            padding: 32px;
            max-width: 500px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 32px;
        }
        
        .demo-title {
            font-size: 28px;
            font-weight: 700;
            color: #333;
            margin-bottom: 8px;
        }
        
        .demo-subtitle {
            color: #666;
            font-size: 16px;
        }
        
        .auth-status {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 24px;
            text-align: center;
        }
        
        .status-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .status-authenticated {
            background: #d4edda;
            color: #155724;
        }
        
        .status-not-authenticated {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-text {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .status-description {
            color: #666;
            font-size: 14px;
        }
        
        .user-info {
            background: #e3f2fd;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 24px;
        }
        
        .user-info h3 {
            color: #1565c0;
            margin-bottom: 12px;
            font-size: 16px;
        }
        
        .user-detail {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .user-detail:last-child {
            margin-bottom: 0;
        }
        
        .user-label {
            color: #666;
            font-weight: 500;
        }
        
        .user-value {
            color: #333;
            font-weight: 600;
        }
        
        .action-buttons {
            display: flex;
            gap: 12px;
            margin-bottom: 24px;
        }
        
        .btn {
            flex: 1;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #1890ff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #40a9ff;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: #f5f5f5;
            color: #333;
        }
        
        .btn-secondary:hover {
            background: #e6e6e6;
        }
        
        .btn-danger {
            background: #ff4d4f;
            color: white;
        }
        
        .btn-danger:hover {
            background: #ff7875;
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }
        
        .org-selector {
            margin-bottom: 24px;
        }
        
        .org-selector label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .org-selector select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e6e6e6;
            border-radius: 8px;
            font-size: 14px;
            background: white;
        }
        
        .org-selector select:focus {
            outline: none;
            border-color: #1890ff;
        }
        
        .demo-footer {
            text-align: center;
            color: #999;
            font-size: 12px;
            margin-top: 24px;
            padding-top: 24px;
            border-top: 1px solid #e6e6e6;
        }
        
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .spinner {
            animation: spin 1s linear infinite;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1 class="demo-title">钉钉认证演示</h1>
            <p class="demo-subtitle">智能网页总结助手 - 钉钉集成功能</p>
        </div>
        
        <!-- 认证状态显示 -->
        <div class="auth-status" id="authStatus">
            <div class="status-icon status-not-authenticated" id="statusIcon">
                🔒
            </div>
            <div class="status-text" id="statusText">未登录</div>
            <div class="status-description" id="statusDescription">请登录钉钉账户以使用集成功能</div>
        </div>
        
        <!-- 用户信息显示 -->
        <div class="user-info" id="userInfo" style="display: none;">
            <h3>用户信息</h3>
            <div class="user-detail">
                <span class="user-label">用户名:</span>
                <span class="user-value" id="userName">-</span>
            </div>
            <div class="user-detail">
                <span class="user-label">邮箱:</span>
                <span class="user-value" id="userEmail">-</span>
            </div>
            <div class="user-detail">
                <span class="user-label">当前组织:</span>
                <span class="user-value" id="currentOrg">-</span>
            </div>
            <div class="user-detail">
                <span class="user-label">可用组织:</span>
                <span class="user-value" id="orgCount">0</span>
            </div>
        </div>
        
        <!-- 组织选择 -->
        <div class="org-selector" id="orgSelector" style="display: none;">
            <label for="orgSelect">选择组织:</label>
            <select id="orgSelect">
                <option value="">请选择组织</option>
            </select>
        </div>
        
        <!-- 操作按钮 -->
        <div class="action-buttons">
            <button class="btn btn-primary" id="loginBtn" onclick="handleLogin()">
                <span>🔑</span>
                登录钉钉
            </button>
            <button class="btn btn-secondary" id="refreshBtn" onclick="handleRefresh()">
                <span>🔄</span>
                刷新状态
            </button>
            <button class="btn btn-danger" id="logoutBtn" onclick="handleLogout()" style="display: none;">
                <span>🚪</span>
                退出登录
            </button>
        </div>
        
        <div class="demo-footer">
            <p>此演示页面展示了Chrome扩展中的钉钉认证功能</p>
            <p>实际使用时，认证状态会在侧边栏和设置页面中显示</p>
        </div>
    </div>

    <script>
        let currentAuthData = null;
        
        // 发送消息到background script
        function sendMessage(message) {
            return new Promise((resolve, reject) => {
                if (!chrome.runtime) {
                    reject(new Error('Chrome runtime不可用，请在扩展环境中运行'));
                    return;
                }
                
                chrome.runtime.sendMessage(message, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                    } else {
                        resolve(response);
                    }
                });
            });
        }
        
        // 更新UI状态
        function updateUI(authData) {
            currentAuthData = authData;
            
            const statusIcon = document.getElementById('statusIcon');
            const statusText = document.getElementById('statusText');
            const statusDescription = document.getElementById('statusDescription');
            const userInfo = document.getElementById('userInfo');
            const orgSelector = document.getElementById('orgSelector');
            const loginBtn = document.getElementById('loginBtn');
            const logoutBtn = document.getElementById('logoutBtn');
            
            if (authData.isAuthenticated && authData.userInfo) {
                // 已认证状态
                statusIcon.className = 'status-icon status-authenticated';
                statusIcon.textContent = '✅';
                statusText.textContent = '已登录';
                statusDescription.textContent = '钉钉账户认证成功';
                
                // 显示用户信息
                userInfo.style.display = 'block';
                document.getElementById('userName').textContent = authData.userInfo.name || '钉钉用户';
                document.getElementById('userEmail').textContent = authData.userInfo.email || authData.userInfo.userId || '-';
                document.getElementById('currentOrg').textContent = authData.selectedOrganization ? authData.selectedOrganization.corpName : '未选择';
                document.getElementById('orgCount').textContent = authData.organizations ? authData.organizations.length : 0;
                
                // 更新组织选择器
                if (authData.organizations && authData.organizations.length > 0) {
                    orgSelector.style.display = 'block';
                    updateOrgSelector(authData.organizations, authData.selectedOrganization);
                } else {
                    orgSelector.style.display = 'none';
                }
                
                // 更新按钮状态
                loginBtn.style.display = 'none';
                logoutBtn.style.display = 'flex';
                
            } else {
                // 未认证状态
                statusIcon.className = 'status-icon status-not-authenticated';
                statusIcon.textContent = '🔒';
                statusText.textContent = '未登录';
                statusDescription.textContent = '请登录钉钉账户以使用集成功能';
                
                // 隐藏用户信息
                userInfo.style.display = 'none';
                orgSelector.style.display = 'none';
                
                // 更新按钮状态
                loginBtn.style.display = 'flex';
                logoutBtn.style.display = 'none';
            }
        }
        
        // 更新组织选择器
        function updateOrgSelector(organizations, selectedOrganization) {
            const orgSelect = document.getElementById('orgSelect');
            orgSelect.innerHTML = '<option value="">请选择组织</option>';
            
            organizations.forEach(org => {
                const option = document.createElement('option');
                option.value = org.corpId;
                option.textContent = org.corpName;
                if (selectedOrganization && selectedOrganization.corpId === org.corpId) {
                    option.selected = true;
                }
                orgSelect.appendChild(option);
            });
            
            // 绑定组织切换事件
            orgSelect.onchange = handleOrgChange;
        }
        
        // 设置加载状态
        function setLoading(isLoading) {
            const container = document.querySelector('.demo-container');
            if (isLoading) {
                container.classList.add('loading');
            } else {
                container.classList.remove('loading');
            }
        }
        
        // 处理登录
        async function handleLogin() {
            setLoading(true);
            
            try {
                const response = await sendMessage({ action: 'initiateDingTalkLogin' });
                
                if (response.success) {
                    alert('登录页面已打开，请在新页面中完成登录，然后点击"刷新状态"按钮');
                } else {
                    alert(`登录失败: ${response.error}`);
                }
                
            } catch (error) {
                alert(`登录异常: ${error.message}`);
            } finally {
                setLoading(false);
            }
        }
        
        // 处理刷新
        async function handleRefresh() {
            setLoading(true);
            
            try {
                const response = await sendMessage({ action: 'getDingTalkAuthStatus' });
                
                if (response.success) {
                    updateUI(response.data);
                } else {
                    alert(`刷新失败: ${response.error}`);
                }
                
            } catch (error) {
                alert(`刷新异常: ${error.message}`);
            } finally {
                setLoading(false);
            }
        }
        
        // 处理登出
        async function handleLogout() {
            if (!confirm('确定要退出钉钉登录吗？')) {
                return;
            }
            
            setLoading(true);
            
            try {
                const response = await sendMessage({ action: 'dingTalkLogout' });
                
                if (response.success) {
                    alert('退出登录成功');
                    await handleRefresh();
                } else {
                    alert(`退出失败: ${response.error}`);
                }
                
            } catch (error) {
                alert(`退出异常: ${error.message}`);
            } finally {
                setLoading(false);
            }
        }
        
        // 处理组织切换
        async function handleOrgChange() {
            const orgSelect = document.getElementById('orgSelect');
            const selectedCorpId = orgSelect.value;
            
            if (!selectedCorpId) return;
            
            setLoading(true);
            
            try {
                const response = await sendMessage({ 
                    action: 'selectDingTalkOrg', 
                    corpId: selectedCorpId 
                });
                
                if (response.success) {
                    await handleRefresh();
                } else {
                    alert(`组织切换失败: ${response.error}`);
                }
                
            } catch (error) {
                alert(`组织切换异常: ${error.message}`);
            } finally {
                setLoading(false);
            }
        }
        
        // 设置钉钉认证状态实时监听
        function setupDingTalkAuthListener() {
            // 监听来自background script的认证状态变化消息
            if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {
                chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
                    if (message.type === 'DINGTALK_AUTH_STATUS_CHANGED') {
                        console.log('收到钉钉认证状态变化通知:', message.data);

                        // 立即更新UI显示
                        updateUI(message.data);

                        return true; // 保持消息通道开放
                    }
                });

                console.log('钉钉认证状态实时监听器已设置');
            } else {
                console.warn('无法设置认证状态监听器，Chrome API不可用');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            // 设置实时监听
            setupDingTalkAuthListener();

            // 延迟加载认证状态
            setTimeout(handleRefresh, 500);
        });
    </script>
</body>
</html>
