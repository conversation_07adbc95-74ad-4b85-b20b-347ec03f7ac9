<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown预览 - 智能网页总结助手</title>
    <link rel="stylesheet" href="markdown-preview.css">
</head>
<body>
    <div class="preview-container">
        <!-- 工具栏 -->
        <div class="toolbar">
            <div class="toolbar-left">
                <h1 class="preview-title">Markdown预览</h1>
                <div class="meta-info" id="metaInfo">
                    <!-- 元信息将在这里显示 -->
                </div>
            </div>
            <div class="toolbar-right">
                <button class="btn btn-secondary" id="copyBtn" title="复制Markdown">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <rect x="9" y="9" width="13" height="13" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                        <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    复制
                </button>
                <button class="btn btn-secondary" id="downloadBtn" title="下载Markdown文件">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" stroke-width="2"/>
                        <polyline points="7,10 12,15 17,10" stroke="currentColor" stroke-width="2"/>
                        <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    下载
                </button>
                <button class="btn btn-primary" id="closeBtn" title="关闭预览">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2"/>
                        <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    关闭
                </button>
            </div>
        </div>

        <!-- 预览内容区域 -->
        <div class="preview-content" id="previewContent">
            <!-- Markdown渲染后的HTML将在这里显示 -->
        </div>

        <!-- 加载状态 -->
        <div class="loading-overlay" id="loadingOverlay" style="display: none;">
            <div class="loading-spinner">
                <div class="spinner"></div>
                <p>正在渲染预览...</p>
            </div>
        </div>
    </div>

    <!-- 引入Markdown解析器 -->
    <script src="../libs/markdown-parser.js"></script>
    <script src="markdown-preview.js"></script>
</body>
</html>
