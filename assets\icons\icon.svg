<?xml version="1.0" encoding="UTF-8"?>
<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="64" cy="64" r="60" fill="url(#gradient1)" />
  
  <!-- 文档图标 -->
  <rect x="32" y="24" width="48" height="64" rx="4" fill="white" opacity="0.9"/>
  <rect x="36" y="32" width="32" height="2" rx="1" fill="url(#gradient2)"/>
  <rect x="36" y="40" width="40" height="2" rx="1" fill="url(#gradient2)"/>
  <rect x="36" y="48" width="36" height="2" rx="1" fill="url(#gradient2)"/>
  <rect x="36" y="56" width="32" height="2" rx="1" fill="url(#gradient2)"/>
  <rect x="36" y="64" width="28" height="2" rx="1" fill="url(#gradient2)"/>
  
  <!-- AI 符号 -->
  <circle cx="88" cy="88" r="16" fill="url(#gradient2)"/>
  <path d="M82 84 L88 90 L94 84" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  <circle cx="88" cy="88" r="3" fill="white"/>
</svg>
