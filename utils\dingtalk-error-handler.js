// 钉钉认证错误处理器
// 处理钉钉认证过程中的各种错误情况

// 错误类型定义
const DINGTALK_ERROR_TYPES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  AUTH_EXPIRED: 'AUTH_EXPIRED',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  ORG_NOT_SELECTED: 'ORG_NOT_SELECTED',
  API_ERROR: 'API_ERROR',
  COOKIE_ERROR: 'COOKIE_ERROR',
  TOKEN_REFRESH_FAILED: 'TOKEN_REFRESH_FAILED',
  SECURITY_VIOLATION: 'SECURITY_VIOLATION',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  CONFIGURATION_ERROR: 'CONFIGURATION_ERROR',
  BROWSER_COMPATIBILITY: 'BROWSER_COMPATIBILITY',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
};

// 消息类型定义
const DINGTALK_MESSAGE_TYPES = {
  // Background → Sidebar/Content
  DT_AUTH_STATUS_CHANGED: 'DT_AUTH_STATUS_CHANGED',
  DT_LOGIN_REQUIRED: 'DT_LOGIN_REQUIRED',
  DT_ORG_SELECTION_REQUIRED: 'DT_ORG_SELECTION_REQUIRED',
  DT_AUTH_ERROR: 'DT_AUTH_ERROR',
  DT_NETWORK_ERROR: 'DT_NETWORK_ERROR',
  DT_PERMISSION_ERROR: 'DT_PERMISSION_ERROR',
  
  // Sidebar/Content → Background
  DT_INITIATE_LOGIN: 'DT_INITIATE_LOGIN',
  DT_GET_AUTH_STATUS: 'DT_GET_AUTH_STATUS',
  DT_SELECT_ORGANIZATION: 'DT_SELECT_ORGANIZATION',
  DT_LOGOUT: 'DT_LOGOUT',
  DT_REFRESH_AUTH: 'DT_REFRESH_AUTH'
};

class DingTalkErrorHandler {
  constructor() {
    this.errorHistory = [];
    this.maxHistorySize = 50;
    this.errorPatterns = this.initializeErrorPatterns();
    this.recoveryStrategies = this.initializeRecoveryStrategies();
    this.userNotificationQueue = [];
    this.lastNotificationTime = 0;
    this.notificationCooldown = 3000; // 3秒通知冷却时间
  }

  // 初始化错误模式匹配
  initializeErrorPatterns() {
    return [
      {
        pattern: /fetch.*failed|network.*error|connection.*refused/i,
        type: DINGTALK_ERROR_TYPES.NETWORK_ERROR,
        severity: 'medium'
      },
      {
        pattern: /401|unauthorized|authentication.*failed/i,
        type: DINGTALK_ERROR_TYPES.AUTH_EXPIRED,
        severity: 'high'
      },
      {
        pattern: /403|forbidden|permission.*denied/i,
        type: DINGTALK_ERROR_TYPES.PERMISSION_DENIED,
        severity: 'high'
      },
      {
        pattern: /429|rate.*limit|too.*many.*requests/i,
        type: DINGTALK_ERROR_TYPES.RATE_LIMIT_EXCEEDED,
        severity: 'medium'
      },
      {
        pattern: /5\d{2}|server.*error|service.*unavailable/i,
        type: DINGTALK_ERROR_TYPES.SERVICE_UNAVAILABLE,
        severity: 'medium'
      },
      {
        pattern: /csrf|security.*violation|invalid.*signature/i,
        type: DINGTALK_ERROR_TYPES.SECURITY_VIOLATION,
        severity: 'high'
      },
      {
        pattern: /token.*refresh.*failed|token.*expired/i,
        type: DINGTALK_ERROR_TYPES.TOKEN_REFRESH_FAILED,
        severity: 'medium'
      },
      {
        pattern: /cookie.*invalid|cookie.*expired/i,
        type: DINGTALK_ERROR_TYPES.COOKIE_ERROR,
        severity: 'medium'
      }
    ];
  }

  // 初始化恢复策略
  initializeRecoveryStrategies() {
    return {
      [DINGTALK_ERROR_TYPES.NETWORK_ERROR]: {
        autoRetry: true,
        maxRetries: 3,
        retryDelay: 2000,
        exponentialBackoff: true,
        userAction: 'check_network'
      },
      [DINGTALK_ERROR_TYPES.AUTH_EXPIRED]: {
        autoRetry: false,
        userAction: 'reauth_required',
        clearAuthState: true
      },
      [DINGTALK_ERROR_TYPES.TOKEN_REFRESH_FAILED]: {
        autoRetry: true,
        maxRetries: 2,
        retryDelay: 5000,
        fallbackAction: 'force_reauth'
      },
      [DINGTALK_ERROR_TYPES.RATE_LIMIT_EXCEEDED]: {
        autoRetry: true,
        maxRetries: 1,
        retryDelay: 10000,
        userAction: 'wait_and_retry'
      },
      [DINGTALK_ERROR_TYPES.SECURITY_VIOLATION]: {
        autoRetry: false,
        userAction: 'security_alert',
        clearAuthState: true,
        reportIncident: true
      }
    };
  }

  // 分析错误类型
  analyzeError(error, context = {}) {
    const traceId = `error_analysis_${Date.now()}`;
    console.log(`[${traceId}] 分析钉钉认证错误:`, error, context);

    // 记录错误历史
    this.recordError(error, context);

    // 使用模式匹配进行智能错误分析
    const errorAnalysis = this.performIntelligentAnalysis(error, context);

    // 生成用户友好的错误信息
    const userFriendlyError = this.generateUserFriendlyError(errorAnalysis);

    // 确定恢复策略
    const recoveryStrategy = this.determineRecoveryStrategy(errorAnalysis.type);

    // 检查是否为重复错误
    const isRecurring = this.checkRecurringError(errorAnalysis.type);

    const result = {
      ...userFriendlyError,
      ...recoveryStrategy,
      traceId: traceId,
      timestamp: Date.now(),
      isRecurring: isRecurring,
      context: context,
      severity: errorAnalysis.severity
    };

    console.log(`[${traceId}] 错误分析结果:`, result);
    return result;
  }

  // 执行智能错误分析
  performIntelligentAnalysis(error, context) {
    const errorMessage = error.message || error.toString();
    const errorName = error.name || 'Unknown';

    // 使用模式匹配
    for (const pattern of this.errorPatterns) {
      if (pattern.pattern.test(errorMessage) || pattern.pattern.test(errorName)) {
        return {
          type: pattern.type,
          severity: pattern.severity,
          matchedPattern: pattern.pattern.source
        };
      }
    }

    // 基于上下文的分析
    if (context.operation === 'cookie_validation' && errorMessage.includes('expired')) {
      return {
        type: DINGTALK_ERROR_TYPES.AUTH_EXPIRED,
        severity: 'high'
      };
    }

    if (context.operation === 'api_request' && error.status) {
      return this.analyzeHttpStatus(error.status);
    }

    // 浏览器兼容性检查
    if (this.checkBrowserCompatibility()) {
      return {
        type: DINGTALK_ERROR_TYPES.BROWSER_COMPATIBILITY,
        severity: 'medium'
      };
    }

    // 默认为未知错误
    return {
      type: DINGTALK_ERROR_TYPES.UNKNOWN_ERROR,
      severity: 'low'
    };
  }

  // 分析HTTP状态码
  analyzeHttpStatus(status) {
    const statusMap = {
      401: { type: DINGTALK_ERROR_TYPES.AUTH_EXPIRED, severity: 'high' },
      403: { type: DINGTALK_ERROR_TYPES.PERMISSION_DENIED, severity: 'high' },
      429: { type: DINGTALK_ERROR_TYPES.RATE_LIMIT_EXCEEDED, severity: 'medium' },
      500: { type: DINGTALK_ERROR_TYPES.SERVICE_UNAVAILABLE, severity: 'medium' },
      502: { type: DINGTALK_ERROR_TYPES.SERVICE_UNAVAILABLE, severity: 'medium' },
      503: { type: DINGTALK_ERROR_TYPES.SERVICE_UNAVAILABLE, severity: 'medium' }
    };

    return statusMap[status] || {
      type: DINGTALK_ERROR_TYPES.API_ERROR,
      severity: 'medium'
    };
  }

  // 检查浏览器兼容性
  checkBrowserCompatibility() {
    try {
      // 在Service Worker环境中，使用不同的检查方式
      if (typeof window === 'undefined') {
        // Service Worker环境，检查基本API
        return !(typeof chrome !== 'undefined' &&
                chrome.cookies &&
                chrome.storage &&
                chrome.runtime &&
                typeof fetch !== 'undefined' &&
                typeof crypto !== 'undefined' &&
                crypto.subtle);
      }

      // 浏览器环境，检查必要的API是否可用
      const requiredAPIs = [
        'chrome.cookies',
        'chrome.storage',
        'chrome.runtime',
        'fetch',
        'crypto.subtle'
      ];

      for (const api of requiredAPIs) {
        const parts = api.split('.');
        let obj = window;

        for (const part of parts) {
          if (!obj || !obj[part]) {
            return true; // 不兼容
          }
          obj = obj[part];
        }
      }

      return false; // 兼容
    } catch (error) {
      return true; // 检查失败，假设不兼容
    }
  }
  // 生成用户友好的错误信息
  generateUserFriendlyError(errorAnalysis) {
    const errorMessages = {
      [DINGTALK_ERROR_TYPES.NETWORK_ERROR]: {
        title: '网络连接问题',
        message: '无法连接到钉钉服务器',
        suggestion: '请检查网络连接，确保能够访问钉钉服务',
        icon: '🌐',
        actions: ['重试', '检查网络设置']
      },
      [DINGTALK_ERROR_TYPES.AUTH_EXPIRED]: {
        title: '登录已过期',
        message: '您的钉钉登录状态已过期',
        suggestion: '请重新登录钉钉账户以继续使用',
        icon: '🔐',
        actions: ['重新登录', '了解更多']
      },
      [DINGTALK_ERROR_TYPES.PERMISSION_DENIED]: {
        title: '权限不足',
        message: '您没有权限执行此操作',
        suggestion: '请联系管理员获取相应权限，或切换到有权限的组织',
        icon: '🚫',
        actions: ['切换组织', '联系管理员']
      },
      [DINGTALK_ERROR_TYPES.TOKEN_REFRESH_FAILED]: {
        title: '认证刷新失败',
        message: '无法刷新您的登录状态',
        suggestion: '系统将尝试自动恢复，如果问题持续请重新登录',
        icon: '🔄',
        actions: ['自动重试', '手动登录']
      },
      [DINGTALK_ERROR_TYPES.RATE_LIMIT_EXCEEDED]: {
        title: '请求过于频繁',
        message: '您的操作过于频繁，请稍后再试',
        suggestion: '为了保护服务稳定性，请等待一段时间后再次尝试',
        icon: '⏱️',
        actions: ['等待重试', '了解限制']
      },
      [DINGTALK_ERROR_TYPES.SECURITY_VIOLATION]: {
        title: '安全验证失败',
        message: '检测到可疑的安全活动',
        suggestion: '为了保护您的账户安全，请重新登录验证身份',
        icon: '🛡️',
        actions: ['重新登录', '安全帮助']
      },
      [DINGTALK_ERROR_TYPES.SERVICE_UNAVAILABLE]: {
        title: '服务暂时不可用',
        message: '钉钉服务器正在维护或遇到临时问题',
        suggestion: '请稍后重试，如果问题持续请联系技术支持',
        icon: '🔧',
        actions: ['稍后重试', '查看状态']
      },
      [DINGTALK_ERROR_TYPES.BROWSER_COMPATIBILITY]: {
        title: '浏览器兼容性问题',
        message: '您的浏览器可能不支持某些功能',
        suggestion: '请更新浏览器到最新版本，或使用Chrome/Edge浏览器',
        icon: '🌐',
        actions: ['更新浏览器', '使用其他浏览器']
      },
      [DINGTALK_ERROR_TYPES.UNKNOWN_ERROR]: {
        title: '未知错误',
        message: '遇到了意外的问题',
        suggestion: '请尝试刷新页面或重新启动扩展',
        icon: '❓',
        actions: ['重试', '报告问题']
      }
    };

    const defaultError = {
      title: '操作失败',
      message: '执行操作时遇到问题',
      suggestion: '请重试或联系技术支持',
      icon: '⚠️',
      actions: ['重试']
    };

    return errorMessages[errorAnalysis.type] || defaultError;
  }

  // 确定恢复策略
  determineRecoveryStrategy(errorType) {
    const strategy = this.recoveryStrategies[errorType];

    if (!strategy) {
      return {
        autoRetry: false,
        userAction: 'manual_retry'
      };
    }

    return {
      autoRetry: strategy.autoRetry || false,
      maxRetries: strategy.maxRetries || 0,
      retryDelay: strategy.retryDelay || 1000,
      exponentialBackoff: strategy.exponentialBackoff || false,
      userAction: strategy.userAction || 'manual_retry',
      clearAuthState: strategy.clearAuthState || false,
      reportIncident: strategy.reportIncident || false,
      fallbackAction: strategy.fallbackAction
    };
  }

  // 检查重复错误
  checkRecurringError(errorType) {
    const recentErrors = this.errorHistory
      .filter(entry => Date.now() - entry.timestamp < 5 * 60 * 1000) // 最近5分钟
      .filter(entry => entry.type === errorType);

    return recentErrors.length >= 3; // 5分钟内同类型错误超过3次
  }

  // 记录错误
  recordError(error, context = {}) {
    const errorRecord = {
      timestamp: Date.now(),
      type: error.name || 'Unknown',
      message: error.message || error.toString(),
      stack: error.stack,
      context: context,
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'service-worker',
      url: typeof window !== 'undefined' && window.location ? window.location.href : 'service-worker'
    };

    this.errorHistory.push(errorRecord);

    // 限制历史记录大小
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory.shift();
    }
  }

  // 处理遗留的HTTP状态码错误分析（保持向后兼容）
  analyzeLegacyHttpError(error) {
    // HTTP状态码错误
    if (error.message && error.message.includes('钉钉API调用失败')) {
      const statusMatch = error.message.match(/(\d{3})/);
      const status = statusMatch ? parseInt(statusMatch[1]) : 0;
      
      switch (status) {
        case 401:
          return {
            type: DINGTALK_ERROR_TYPES.AUTH_EXPIRED,
            message: '钉钉登录已过期，请重新登录',
            suggestion: '点击下方按钮重新登录钉钉账户',
            retry: false,
            action: 'reauth',
            icon: this.getErrorIcon('auth')
          };
          
        case 403:
          return {
            type: DINGTALK_ERROR_TYPES.PERMISSION_DENIED,
            message: '权限不足，无法访问该功能',
            suggestion: '请联系管理员获取相应权限',
            retry: false,
            icon: this.getErrorIcon('permission')
          };
          
        case 429:
          return {
            type: DINGTALK_ERROR_TYPES.API_ERROR,
            message: '请求过于频繁，请稍后重试',
            suggestion: '请等待一段时间后再次尝试',
            retry: true,
            retryDelay: 5000,
            icon: this.getErrorIcon('rate-limit')
          };
          
        case 500:
        case 502:
        case 503:
          return {
            type: DINGTALK_ERROR_TYPES.API_ERROR,
            message: '钉钉服务暂时不可用',
            suggestion: '服务器正在维护，请稍后重试',
            retry: true,
            retryDelay: 10000,
            icon: this.getErrorIcon('server')
          };
          
        default:
          return {
            type: DINGTALK_ERROR_TYPES.API_ERROR,
            message: `API调用失败 (${status})`,
            suggestion: '请稍后重试或联系技术支持',
            retry: true,
            icon: this.getErrorIcon('api')
          };
      }
    }
    
    // Cookie相关错误
    if (error.message && error.message.includes('Cookie')) {
      return {
        type: DINGTALK_ERROR_TYPES.COOKIE_ERROR,
        message: '认证信息异常',
        suggestion: '请清理浏览器缓存后重新登录',
        retry: false,
        action: 'clear-cache',
        icon: this.getErrorIcon('cookie')
      };
    }
    
    // 组织未选择错误
    if (error.message && error.message.includes('组织')) {
      return {
        type: DINGTALK_ERROR_TYPES.ORG_NOT_SELECTED,
        message: '请选择要使用的组织',
        suggestion: '在设置页面中选择您要使用的钉钉组织',
        retry: false,
        action: 'select-org',
        icon: this.getErrorIcon('organization')
      };
    }
    
      // 通用错误
      return {
        type: DINGTALK_ERROR_TYPES.UNKNOWN_ERROR,
        message: error.message || '发生未知错误',
        suggestion: '请刷新页面后重试，如问题持续请联系技术支持',
        retry: true,
        icon: this.getErrorIcon('unknown')
      };
    }
  }

  // 获取错误图标
  getErrorIcon(type) {
    const icons = {
      network: `<path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>`,
      
      auth: `<path d="M9 12L11 14L15 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
             <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2"/>`,
      
      permission: `<path d="M12 1L3 5V11C3 16 6 20 12 23C18 20 21 16 21 11V5L12 1Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                   <path d="M9 12L11 14L15 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>`,
      
      'rate-limit': `<circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                     <polyline points="12,6 12,12 16,14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>`,
      
      server: `<rect x="2" y="3" width="20" height="14" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
               <line x1="8" y1="21" x2="16" y2="21" stroke="currentColor" stroke-width="2"/>
               <line x1="12" y1="17" x2="12" y2="21" stroke="currentColor" stroke-width="2"/>`,
      
      api: `<polygon points="12 2 2 7 12 12 22 7 12 2" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
            <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
            <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>`,
      
      cookie: `<circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
               <path d="M12 1V3" stroke="currentColor" stroke-width="2"/>
               <path d="M12 21V23" stroke="currentColor" stroke-width="2"/>
               <path d="M4.22 4.22L5.64 5.64" stroke="currentColor" stroke-width="2"/>
               <path d="M18.36 18.36L19.78 19.78" stroke="currentColor" stroke-width="2"/>`,
      
      organization: `<path d="M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21" stroke="currentColor" stroke-width="2"/>
                     <circle cx="9" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                     <path d="M23 21V19C23 18.1645 22.7155 17.3541 22.2094 16.6977C21.7033 16.0414 20.9983 15.5735 20.2 15.3613" stroke="currentColor" stroke-width="2"/>`,
      
      unknown: `<circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                <line x1="12" y1="8" x2="12" y2="12" stroke="currentColor" stroke-width="2"/>
                <line x1="12" y1="16" x2="12.01" y2="16" stroke="currentColor" stroke-width="2"/>`
    };
    
    return icons[type] || icons.unknown;
  }

  // 记录错误历史
  recordError(error) {
    const errorRecord = {
      timestamp: new Date().toISOString(),
      message: error.message,
      stack: error.stack,
      type: error.name || 'Error'
    };
    
    this.errorHistory.unshift(errorRecord);
    
    // 限制历史记录大小
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory = this.errorHistory.slice(0, this.maxHistorySize);
    }
  }

  // 处理认证错误
  async handleAuthError(error, context = {}) {
    const errorInfo = this.analyzeError(error);
    const traceId = context.traceId || `error_${Date.now()}`;
    
    console.error(`[${traceId}] 钉钉认证错误:`, errorInfo);
    
    // 根据错误类型执行相应的处理
    switch (errorInfo.type) {
      case DINGTALK_ERROR_TYPES.AUTH_EXPIRED:
        return this.handleAuthExpired(errorInfo, context, traceId);
        
      case DINGTALK_ERROR_TYPES.PERMISSION_DENIED:
        return this.handlePermissionDenied(errorInfo, context, traceId);
        
      case DINGTALK_ERROR_TYPES.ORG_NOT_SELECTED:
        return this.handleOrgNotSelected(errorInfo, context, traceId);
        
      case DINGTALK_ERROR_TYPES.NETWORK_ERROR:
        return this.handleNetworkError(errorInfo, context, traceId);
        
      default:
        return this.handleGenericError(errorInfo, context, traceId);
    }
  }

  // 处理认证过期
  async handleAuthExpired(errorInfo, context, traceId) {
    console.log(`[${traceId}] 处理认证过期`);
    
    // 清理认证状态
    if (context.authManager) {
      await context.authManager.clearAuthState();
    }
    
    // 发送消息给UI
    if (context.tabId) {
      await this.sendTabMessage(context.tabId, DINGTALK_MESSAGE_TYPES.DT_LOGIN_REQUIRED, {
        errorInfo: errorInfo,
        traceId: traceId
      });
    }
    
    return {
      handled: true,
      action: 'login_required',
      errorInfo: errorInfo
    };
  }

  // 处理权限不足
  async handlePermissionDenied(errorInfo, context, traceId) {
    console.log(`[${traceId}] 处理权限不足`);
    
    if (context.tabId) {
      await this.sendTabMessage(context.tabId, DINGTALK_MESSAGE_TYPES.DT_PERMISSION_ERROR, {
        errorInfo: errorInfo,
        traceId: traceId
      });
    }
    
    return {
      handled: true,
      action: 'permission_denied',
      errorInfo: errorInfo
    };
  }

  // 处理组织未选择
  async handleOrgNotSelected(errorInfo, context, traceId) {
    console.log(`[${traceId}] 处理组织未选择`);
    
    if (context.tabId) {
      await this.sendTabMessage(context.tabId, DINGTALK_MESSAGE_TYPES.DT_ORG_SELECTION_REQUIRED, {
        errorInfo: errorInfo,
        traceId: traceId
      });
    }
    
    return {
      handled: true,
      action: 'org_selection_required',
      errorInfo: errorInfo
    };
  }

  // 处理网络错误
  async handleNetworkError(errorInfo, context, traceId) {
    console.log(`[${traceId}] 处理网络错误`);
    
    if (context.tabId) {
      await this.sendTabMessage(context.tabId, DINGTALK_MESSAGE_TYPES.DT_NETWORK_ERROR, {
        errorInfo: errorInfo,
        traceId: traceId,
        canRetry: errorInfo.retry
      });
    }
    
    return {
      handled: true,
      action: 'network_error',
      errorInfo: errorInfo
    };
  }

  // 处理通用错误
  async handleGenericError(errorInfo, context, traceId) {
    console.log(`[${traceId}] 处理通用错误`);
    
    if (context.tabId) {
      await this.sendTabMessage(context.tabId, DINGTALK_MESSAGE_TYPES.DT_AUTH_ERROR, {
        errorInfo: errorInfo,
        traceId: traceId,
        canRetry: errorInfo.retry
      });
    }
    
    return {
      handled: true,
      action: 'generic_error',
      errorInfo: errorInfo
    };
  }

  // 发送消息给标签页
  async sendTabMessage(tabId, messageType, data) {
    try {
      await chrome.tabs.sendMessage(tabId, {
        type: messageType,
        data: data
      });
      console.log(`消息已发送给标签页 ${tabId}:`, messageType);
    } catch (error) {
      console.error(`发送消息给标签页 ${tabId} 失败:`, error);
    }
  }

  // 重试机制
  async retryWithBackoff(fn, maxRetries = 3, baseDelay = 1000) {
    let lastError;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        
        if (attempt === maxRetries) {
          break;
        }
        
        const delay = baseDelay * Math.pow(2, attempt - 1);
        console.log(`重试第 ${attempt} 次，${delay}ms 后重试`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError;
  }

  // 获取错误历史
  getErrorHistory() {
    return [...this.errorHistory];
  }

  // 清理错误历史
  clearErrorHistory() {
    this.errorHistory = [];
  }

  // 生成错误报告
  generateErrorReport() {
    return {
      timestamp: new Date().toISOString(),
      totalErrors: this.errorHistory.length,
      recentErrors: this.errorHistory.slice(0, 10),
      errorTypes: this.getErrorTypeStats()
    };
  }

  // 获取错误类型统计
  getErrorTypeStats() {
    const stats = {};
    this.errorHistory.forEach(error => {
      const type = error.type || 'Unknown';
      stats[type] = (stats[type] || 0) + 1;
    });
    return stats;
  }
}

// 导出
if (typeof window !== 'undefined') {
  window.DingTalkErrorHandler = DingTalkErrorHandler;
  window.DINGTALK_ERROR_TYPES = DINGTALK_ERROR_TYPES;
  window.DINGTALK_MESSAGE_TYPES = DINGTALK_MESSAGE_TYPES;
}
