# 钉钉文档闪存认证环境参数

## 环境配置

### 构建环境变量
- **开发环境**: `BUILD_ENV=pre` (预发环境)
- **生产环境**: 默认 (无BUILD_ENV变量)

### 钉钉域名配置
```javascript
// 环境域名映射
const DINGTALK_DOMAINS = {
    production: "https://docs.dingtalk.com",
    pre: "https://pre-docs.dingtalk.com"
};
```

## 认证相关URL端点

### 1. 登录认证页面
- **生产环境**: `https://docs.dingtalk.com/i`
- **预发环境**: `https://pre-docs.dingtalk.com/i`
- **URL模式**: `${baseUrl}/i`

### 2. API端点
基于代码分析，主要API端点包括：

#### 用户组织信息API
- **端点**: `/portal/api/v1/mine/orgs`
- **参数**: `?orgTypes=${types}`
- **完整URL**: `${baseUrl}/portal/api/v1/mine/orgs?orgTypes=${types}`

#### 用户设置API
- **端点**: `/openapi/api/user/settings`
- **完整URL**: `${baseUrl}/openapi/api/user/settings`

#### 其他可能的API端点
- `/portal/api/v1/*` - 门户相关API
- `/openapi/api/*` - 开放API接口
- `/box/api/*` - 文档盒子相关API
- `/core/api/*` - 核心服务API

## 存储键配置

### Chrome Storage Local 键名
```javascript
const STORAGE_KEYS = {
    LOGIN_KEY: "Login",           // 登录状态
    MY_ORGS_KEY: "MY_ORGS_KEY",  // 用户组织信息
    SELECTED_CORP_ID: "selectedCorpId", // 选中的企业ID
    USER_SETTINGS: "userSettings" // 用户设置
};
```

## Cookie配置

### 认证Cookie
- **域名**: `.dingtalk.com`
- **Cookie名**: `account`
- **作用**: 用户认证状态标识

### Cookie监听配置
```javascript
// 监听钉钉域名下的account cookie变化
chrome.cookies.onChanged.addListener((changeInfo) => {
    const {removed, cookie} = changeInfo;
    const {name, domain} = cookie;
    
    if (domain === ".dingtalk.com" && name === "account") {
        // 处理认证状态变化
    }
});
```

## Chrome扩展权限配置

### 必需权限
```json
{
    "permissions": [
        "tabs",        // 标签页访问
        "activeTab",   // 活动标签页
        "cookies",     // Cookie访问
        "storage",     // 本地存储
        "contextMenus", // 右键菜单
        "scripting"    // 脚本注入
    ],
    "host_permissions": [
        "https://*.dingtalk.com/*"  // 钉钉域名权限
    ]
}
```

## 消息通信配置

### 认证相关消息类型
```javascript
const MESSAGE_TYPES = {
    // Background -> Content Script
    CS_NOT_AUTHORIZED: "CS_NOT_AUTHORIZED",  // 未授权
    CS_NO_ORG: "CS_NO_ORG",                 // 无组织权限
    
    // Content Script -> Background
    BG_OPEN_LOGIN_PAGE: "BG_OPEN_LOGIN_PAGE", // 打开登录页
    BG_GET_SELECTED_CORP: "BG_GET_SELECTED_CORP", // 获取选中企业
};
```

## 环境检测逻辑

### 环境判断方法
```javascript
// 基于BUILD_ENV环境变量判断
function getCurrentEnvironment() {
    return process.env.BUILD_ENV === 'pre' ? 'pre' : 'production';
}

// 获取对应环境的域名
function getCurrentDomain() {
    const env = getCurrentEnvironment();
    return DINGTALK_DOMAINS[env];
}
```

## 阿里内部配置

### NPM注册表
- **注册表URL**: `https://registry.anpm.alibaba-inc.com`
- **用途**: 阿里内部包依赖管理

### 内部依赖包
```json
{
    "@ali/dingdoc-portal-data": "2.28.0",
    "@ali/dingdoc-portal-html-cut-lib": "0.0.3",
    "@ali/dingdoc-weblwp-client": "0.2.1-beta.0",
    "@ali/dingtalk-jsapi": "2.13.95"
}
```

## 安全配置

### 内容安全策略 (CSP)
```json
{
    "content_security_policy": {
        "extension_pages": "script-src 'self'; object-src 'self'"
    }
}
```

### 跨域配置
- **Web Accessible Resources**: 允许网页访问特定资源
- **Message Channel**: 跨域消息通信机制

## 开发环境配置

### 构建命令
```bash
# 开发构建 (预发环境)
yarn start  # BUILD_ENV=pre webpack --config webpack.config.js

# 生产构建
yarn build  # webpack --config webpack.config.js
```

### 包管理器
- **工具**: Yarn 1.22.19
- **配置**: 使用阿里内部注册表

## 调试配置

### Chrome扩展调试
1. 打开 `chrome://extensions/`
2. 启用"开发者模式"
3. 加载解压的扩展程序
4. 查看Service Worker日志

### 网络调试
- 监控钉钉域名下的网络请求
- 检查Cookie设置和变化
- 验证API调用响应