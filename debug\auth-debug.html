<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钉钉认证调试工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.danger {
            background: #dc3545;
        }
        .btn.danger:hover {
            background: #c82333;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .data-display {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 钉钉认证调试工具</h1>
        
        <div class="grid">
            <!-- 认证状态检查 -->
            <div class="section">
                <h3>认证状态检查</h3>
                <button class="btn" onclick="checkAuthStatus()">检查认证状态</button>
                <button class="btn" onclick="refreshAuthStatus()">刷新认证状态</button>
                <button class="btn" onclick="forceAuthCheck()">强制检查状态</button>
                <div id="authStatus" class="status info">点击按钮检查认证状态</div>
                <div id="authData" class="data-display"></div>
            </div>

            <!-- Cookie检查 -->
            <div class="section">
                <h3>Cookie检查</h3>
                <button class="btn" onclick="checkCookies()">检查钉钉Cookie</button>
                <button class="btn" onclick="validateCookies()">验证Cookie有效性</button>
                <div id="cookieStatus" class="status info">点击按钮检查Cookie</div>
                <div id="cookieData" class="data-display"></div>
            </div>

            <!-- 消息传递测试 -->
            <div class="section">
                <h3>消息传递测试</h3>
                <button class="btn" onclick="testMessagePassing()">测试消息传递</button>
                <button class="btn" onclick="simulateAuthChange()">模拟认证变化</button>
                <div id="messageStatus" class="status info">点击按钮测试消息传递</div>
                <div id="messageData" class="data-display"></div>
            </div>

            <!-- 实时监听测试 -->
            <div class="section">
                <h3>实时监听测试</h3>
                <button class="btn" onclick="setupListener()">设置监听器</button>
                <button class="btn" onclick="triggerLogin()">触发登录</button>
                <div id="listenerStatus" class="status info">点击按钮设置监听器</div>
                <div id="listenerData" class="data-display"></div>
            </div>
        </div>

        <!-- 操作日志 -->
        <div class="section">
            <h3>操作日志</h3>
            <button class="btn" onclick="clearLog()">清空日志</button>
            <div id="debugLog" class="log">等待操作...</div>
        </div>
    </div>

    <script>
        let messageListener = null;
        
        function log(message) {
            const logElement = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('debugLog').textContent = '';
        }

        function setStatus(elementId, type, message) {
            const element = document.getElementById(elementId);
            element.className = `status ${type}`;
            element.textContent = message;
        }

        function setData(elementId, data) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
        }

        // 检查认证状态
        async function checkAuthStatus() {
            log('开始检查认证状态...');
            try {
                const response = await sendMessage({ action: 'getDingTalkAuthStatus' });
                
                if (response.success) {
                    setStatus('authStatus', 'success', '认证状态获取成功');
                    setData('authData', response.data);
                    log(`认证状态: ${response.data.isAuthenticated ? '已认证' : '未认证'}`);
                    if (response.data.userInfo) {
                        log(`用户: ${response.data.userInfo.name}`);
                    }
                } else {
                    setStatus('authStatus', 'error', `获取认证状态失败: ${response.error}`);
                    log(`获取认证状态失败: ${response.error}`);
                }
            } catch (error) {
                setStatus('authStatus', 'error', `检查认证状态异常: ${error.message}`);
                log(`检查认证状态异常: ${error.message}`);
            }
        }

        // 刷新认证状态
        async function refreshAuthStatus() {
            log('开始刷新认证状态...');
            try {
                const response = await sendMessage({ action: 'refreshDingTalkAuth' });

                if (response.success) {
                    setStatus('authStatus', 'success', '认证状态刷新成功');
                    setData('authData', response.data);
                    log(`刷新后认证状态: ${response.data.isAuthenticated ? '已认证' : '未认证'}`);
                } else {
                    setStatus('authStatus', 'error', `刷新认证状态失败: ${response.error}`);
                    log(`刷新认证状态失败: ${response.error}`);
                }
            } catch (error) {
                setStatus('authStatus', 'error', `刷新认证状态异常: ${error.message}`);
                log(`刷新认证状态异常: ${error.message}`);
            }
        }

        // 强制检查认证状态
        async function forceAuthCheck() {
            log('开始强制检查认证状态...');
            try {
                const response = await sendMessage({ action: 'forceDingTalkAuthCheck' });

                if (response.success) {
                    const { authStatus, cookieValid, actionTaken, traceId } = response.data;

                    setStatus('authStatus', 'success', `强制检查完成 - 操作: ${actionTaken}`);
                    setData('authData', authStatus);

                    log(`[${traceId}] 强制检查结果:`);
                    log(`  - Cookie有效性: ${cookieValid}`);
                    log(`  - 认证状态: ${authStatus.isAuthenticated ? '已认证' : '未认证'}`);
                    log(`  - 执行操作: ${actionTaken}`);

                    if (actionTaken === 'triggered_login') {
                        log('🎉 检测到登录状态，已触发登录处理');
                    } else if (actionTaken === 'triggered_logout') {
                        log('👋 检测到登出状态，已触发登出处理');
                    } else {
                        log('✅ 状态一致，无需额外操作');
                    }
                } else {
                    setStatus('authStatus', 'error', `强制检查失败: ${response.error}`);
                    log(`强制检查失败: ${response.error}`);
                }
            } catch (error) {
                setStatus('authStatus', 'error', `强制检查异常: ${error.message}`);
                log(`强制检查异常: ${error.message}`);
            }
        }

        // 检查Cookie
        async function checkCookies() {
            log('开始检查钉钉Cookie...');
            try {
                const cookies = await chrome.cookies.getAll({ domain: '.dingtalk.com' });
                
                setStatus('cookieStatus', 'success', `找到 ${cookies.length} 个钉钉Cookie`);
                
                const authCookies = cookies.filter(c => 
                    ['account', 'login_aliyunid_ticket', '_tb_token_'].includes(c.name) && c.value
                );
                
                const cookieInfo = {
                    totalCookies: cookies.length,
                    authCookies: authCookies.length,
                    cookies: authCookies.map(c => ({
                        name: c.name,
                        hasValue: !!c.value,
                        valueLength: c.value ? c.value.length : 0,
                        domain: c.domain,
                        expirationDate: c.expirationDate
                    }))
                };
                
                setData('cookieData', cookieInfo);
                log(`找到 ${cookies.length} 个钉钉Cookie，其中 ${authCookies.length} 个认证Cookie`);
                
            } catch (error) {
                setStatus('cookieStatus', 'error', `检查Cookie失败: ${error.message}`);
                log(`检查Cookie失败: ${error.message}`);
            }
        }

        // 验证Cookie有效性
        async function validateCookies() {
            log('开始验证Cookie有效性...');
            try {
                // 这里需要调用background script的验证方法
                const response = await sendMessage({ action: 'validateDingTalkCookies' });
                
                if (response.success) {
                    setStatus('cookieStatus', 'success', `Cookie验证结果: ${response.data.isValid ? '有效' : '无效'}`);
                    setData('cookieData', response.data);
                    log(`Cookie验证结果: ${response.data.isValid ? '有效' : '无效'}`);
                } else {
                    setStatus('cookieStatus', 'error', `Cookie验证失败: ${response.error}`);
                    log(`Cookie验证失败: ${response.error}`);
                }
            } catch (error) {
                setStatus('cookieStatus', 'error', `Cookie验证异常: ${error.message}`);
                log(`Cookie验证异常: ${error.message}`);
            }
        }

        // 测试消息传递
        async function testMessagePassing() {
            log('开始测试消息传递...');
            try {
                const testMessage = { action: 'ping', timestamp: Date.now() };
                const response = await sendMessage(testMessage);
                
                setStatus('messageStatus', 'success', '消息传递测试成功');
                setData('messageData', { sent: testMessage, received: response });
                log('消息传递测试成功');
                
            } catch (error) {
                setStatus('messageStatus', 'error', `消息传递测试失败: ${error.message}`);
                log(`消息传递测试失败: ${error.message}`);
            }
        }

        // 模拟认证变化
        async function simulateAuthChange() {
            log('模拟认证状态变化...');
            
            const mockAuthData = {
                isAuthenticated: true,
                userInfo: { name: '测试用户', avatar: '', userId: 'test123', email: '<EMAIL>' },
                organizations: [{ corpId: 'corp1', corpName: '测试组织' }],
                selectedCorpId: 'corp1',
                selectedOrganization: { corpId: 'corp1', corpName: '测试组织' },
                timestamp: Date.now()
            };

            // 模拟发送认证状态变化消息
            if (messageListener) {
                const message = {
                    type: 'DINGTALK_AUTH_STATUS_CHANGED',
                    data: mockAuthData,
                    traceId: `simulate_${Date.now()}`
                };
                
                messageListener(message, {}, () => {});
                setStatus('messageStatus', 'success', '模拟认证变化消息已发送');
                log('模拟认证变化消息已发送');
            } else {
                setStatus('messageStatus', 'error', '请先设置监听器');
                log('请先设置监听器');
            }
        }

        // 设置监听器
        function setupListener() {
            log('设置认证状态变化监听器...');
            
            if (chrome.runtime && chrome.runtime.onMessage) {
                messageListener = (message, sender, sendResponse) => {
                    if (message.type === 'DINGTALK_AUTH_STATUS_CHANGED') {
                        log('收到认证状态变化通知');
                        setStatus('listenerStatus', 'success', '收到认证状态变化通知');
                        setData('listenerData', message.data);
                        return true;
                    }
                };
                
                chrome.runtime.onMessage.addListener(messageListener);
                setStatus('listenerStatus', 'success', '监听器已设置');
                log('认证状态变化监听器已设置');
            } else {
                setStatus('listenerStatus', 'error', 'Chrome API不可用');
                log('Chrome API不可用，无法设置监听器');
            }
        }

        // 触发登录
        async function triggerLogin() {
            log('触发钉钉登录...');
            try {
                const response = await sendMessage({ action: 'initiateDingTalkLogin' });
                
                if (response.success) {
                    setStatus('listenerStatus', 'success', '登录页面已打开，等待认证状态变化...');
                    log('登录页面已打开，等待认证状态变化...');
                } else {
                    setStatus('listenerStatus', 'error', `触发登录失败: ${response.error}`);
                    log(`触发登录失败: ${response.error}`);
                }
            } catch (error) {
                setStatus('listenerStatus', 'error', `触发登录异常: ${error.message}`);
                log(`触发登录异常: ${error.message}`);
            }
        }

        // 发送消息到background script
        function sendMessage(message) {
            return new Promise((resolve, reject) => {
                chrome.runtime.sendMessage(message, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                    } else {
                        resolve(response);
                    }
                });
            });
        }

        // 页面加载完成后自动设置监听器
        document.addEventListener('DOMContentLoaded', () => {
            log('调试工具已加载');
            setupListener();
        });
    </script>
</body>
</html>
