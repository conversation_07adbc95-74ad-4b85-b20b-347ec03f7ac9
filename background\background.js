// Service Worker for Chrome Extension
// 处理扩展的后台逻辑

// 导入钉钉认证相关模块
try {
  importScripts('../utils/dingtalk-security.js');
  importScripts('../utils/dingtalk-error-handler.js');
  importScripts('../utils/dingtalk-auth.js');
  console.log('所有钉钉认证模块导入成功');
} catch (error) {
  console.error('导入钉钉认证模块失败:', error);
}

// 硬编码的API配置
const HARDCODED_API_CONFIG = {
  apiKey: 'sk-466900693bb54313bb9c9a5feb986eb4',
  baseUrl: 'https://dashscope.aliyuncs.com/compatible-mode/v1'
};

// 全局钉钉认证管理器实例
let dingTalkAuthManager = null;
let dingTalkErrorHandler = null;

// 立即初始化钉钉认证管理器（确保Cookie监听器尽早设置）
async function initializeDingTalkAuth() {
  try {
    if (!dingTalkAuthManager) {
      dingTalkAuthManager = new DingTalkAuthManager();
      dingTalkErrorHandler = new DingTalkErrorHandler();
      console.log('钉钉认证管理器初始化完成');
    }
  } catch (error) {
    console.error('钉钉认证管理器初始化失败:', error);
  }
}

// Service Worker启动时立即初始化
initializeDingTalkAuth();

// Service Worker启动事件（确保每次激活都重新设置监听器）
chrome.runtime.onStartup.addListener(async () => {
  console.log('Service Worker启动，重新初始化钉钉认证管理器');
  await initializeDingTalkAuth();
});

// 安装事件
chrome.runtime.onInstalled.addListener(async (details) => {
  console.log('智能网页总结助手已安装');

  // 确保钉钉认证管理器已初始化
  await initializeDingTalkAuth();

  // 设置默认配置
  chrome.storage.sync.set({
    apiConfig: {
      provider: 'qwen',
      apiKey: HARDCODED_API_CONFIG.apiKey,
      baseUrl: HARDCODED_API_CONFIG.baseUrl,
      model: 'qwen-plus'
    },
    promptTemplates: {
      default: {
        name: '默认总结',
        prompt: '请对以下内容进行总结，提取关键信息和要点：\n\n{content}'
      },
      news: {
        name: '新闻总结',
        prompt: '请总结这篇新闻的主要内容，包括：\n1. 核心事件\n2. 关键人物\n3. 时间地点\n4. 影响和意义\n\n内容：{content}'
      },
      academic: {
        name: '学术文章',
        prompt: '请总结这篇学术文章，包括：\n1. 研究目的和问题\n2. 主要方法\n3. 核心发现\n4. 结论和意义\n\n内容：{content}'
      },
      technical: {
        name: '技术文档',
        prompt: '请总结这份技术文档的要点：\n1. 主要功能特性\n2. 使用方法\n3. 注意事项\n4. 适用场景\n\n内容：{content}'
      }
    },
    selectedTemplate: 'default',
    uiSettings: {
      theme: 'dark',
      sidebarWidth: 380,
      autoSummarize: false
    },
    // 新增钉钉认证相关默认配置
    dingTalkConfig: {
      enabled: true,
      autoLogin: false,
      environment: 'production'
    }
  });
});

// 处理扩展图标点击事件
chrome.action.onClicked.addListener(async (tab) => {
  try {
    // 打开侧边栏
    await chrome.sidePanel.open({ tabId: tab.id });
  } catch (error) {
    console.error('打开侧边栏失败:', error);
  }
});

// 处理来自content script和sidebar的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('收到消息:', request);

  // 处理钉钉认证状态变化广播消息
  if (request.type === 'DINGTALK_AUTH_STATUS_CHANGED') {
    // 这是来自认证管理器的广播消息，转发给所有扩展页面
    console.log('转发钉钉认证状态变化消息:', request.data);
    return false; // 不需要响应
  }

  switch (request.action) {
    case 'extractContent':
      handleContentExtraction(request, sender, sendResponse);
      break;
    case 'summarizeContent':
      handleSummarization(request, sender, sendResponse);
      break;
    case 'extractMarkdown':
      handleMarkdownExtraction(request, sender, sendResponse);
      break;
    case 'getConfig':
      handleGetConfig(sendResponse);
      break;
    case 'saveConfig':
      handleSaveConfig(request.config, sendResponse);
      break;
    case 'testConnection':
      handleTestConnection(request.apiConfig, sendResponse);
      break;
    case 'exportConfig':
      handleExportConfig(sendResponse);
      break;
    case 'importConfig':
      handleImportConfig(request.importData, sendResponse);
      break;
    case 'clearHistory':
      handleClearHistory(sendResponse);
      break;
    case 'resetSettings':
      handleResetSettings(sendResponse);
      break;
    case 'addHistoryRecord':
      handleAddHistoryRecord(request.record, sendResponse);
      break;
    // 钉钉认证相关消息处理
    case 'getDingTalkAuthStatus':
      handleGetDingTalkAuthStatus(sendResponse);
      break;
    case 'initiateDingTalkLogin':
      handleInitiateDingTalkLogin(sendResponse);
      break;
    case 'selectDingTalkOrg':
      handleSelectDingTalkOrg(request.corpId, sendResponse);
      break;
    case 'dingTalkLogout':
      handleDingTalkLogout(sendResponse);
      break;
    case 'refreshDingTalkAuth':
      handleRefreshDingTalkAuth(sendResponse);
      break;
    // 调试相关消息处理
    case 'validateDingTalkCookies':
      handleValidateDingTalkCookies(sendResponse);
      break;
    case 'forceDingTalkAuthCheck':
      handleForceDingTalkAuthCheck(sendResponse);
      break;
    case 'getDingTalkDebugInfo':
      handleGetDingTalkDebugInfo(sendResponse);
      break;
    case 'ping':
      sendResponse({ success: true, message: 'pong', timestamp: Date.now() });
      break;
    default:
      sendResponse({ error: '未知的操作类型' });
  }

  return true; // 保持消息通道开放
});

// 处理内容提取
async function handleContentExtraction(request, sender, sendResponse) {
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    
    // 注入内容提取脚本
    const results = await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      function: extractPageContent
    });
    
    if (results && results[0]) {
      sendResponse({ 
        success: true, 
        content: results[0].result,
        url: tab.url,
        title: tab.title
      });
    } else {
      sendResponse({ error: '内容提取失败' });
    }
  } catch (error) {
    console.error('内容提取错误:', error);
    sendResponse({ error: error.message });
  }
}

// 处理AI总结
async function handleSummarization(request, sender, sendResponse) {
  try {
    const config = await chrome.storage.sync.get(['apiConfig', 'promptTemplates', 'selectedTemplate']);
    const storedApiConfig = config.apiConfig;
    const template = config.promptTemplates[config.selectedTemplate] || config.promptTemplates.default;

    // 使用硬编码的API配置
    const apiConfig = {
      ...storedApiConfig,
      apiKey: HARDCODED_API_CONFIG.apiKey,
      baseUrl: HARDCODED_API_CONFIG.baseUrl
    };

    if (!apiConfig.apiKey) {
      sendResponse({ error: 'API密钥配置错误' });
      return;
    }

    // 清理内容，确保不包含可能导致编码问题的字符
    const cleanContent = request.content.replace(/[\u0000-\u001F\u007F-\u009F]/g, '');

    // 替换提示词中的变量
    const prompt = template.prompt.replace('{content}', cleanContent);

    // 调用AI API
    const summary = await callAIAPI(apiConfig, prompt);

    sendResponse({
      success: true,
      summary: summary,
      template: template.name
    });
  } catch (error) {
    console.error('AI总结错误:', error);
    sendResponse({ error: error.message });
  }
}

// 处理Markdown提取
async function handleMarkdownExtraction(request, sender, sendResponse) {
  try {
    const config = await chrome.storage.sync.get(['apiConfig']);
    const storedApiConfig = config.apiConfig;

    // 使用硬编码的API配置
    const apiConfig = {
      ...storedApiConfig,
      apiKey: HARDCODED_API_CONFIG.apiKey,
      baseUrl: HARDCODED_API_CONFIG.baseUrl
    };

    if (!apiConfig.apiKey) {
      sendResponse({ error: 'API密钥配置错误' });
      return;
    }

    // 清理内容，确保不包含可能导致编码问题的字符
    const cleanContent = request.content.replace(/[\u0000-\u001F\u007F-\u009F]/g, '');

    // 检查内容长度，如果过长则分块处理
    const content = cleanContent;
    const maxChunkSize = 6000; // 保留一些余量给提示词

    if (content.length <= maxChunkSize) {
      // 单块处理
      const markdown = await extractToMarkdown(apiConfig, content, request.title, request.url);
      sendResponse({
        success: true,
        markdown: markdown,
        chunks: 1
      });
    } else {
      // 分块处理
      const chunks = splitContentIntoChunks(content, maxChunkSize);
      const markdownChunks = [];

      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];
        const chunkMarkdown = await extractToMarkdown(
          apiConfig,
          chunk,
          request.title,
          request.url,
          i + 1,
          chunks.length
        );
        markdownChunks.push(chunkMarkdown);

        // 发送进度更新
        if (request.progressCallback) {
          const progress = Math.round(((i + 1) / chunks.length) * 100);
          // 注意：这里无法直接回调，需要通过其他方式通知进度
        }
      }

      // 合并所有块
      const finalMarkdown = combineMarkdownChunks(markdownChunks, request.title, request.url);

      sendResponse({
        success: true,
        markdown: finalMarkdown,
        chunks: chunks.length
      });
    }
  } catch (error) {
    console.error('Markdown提取错误:', error);
    sendResponse({ error: formatErrorMessage(error) });
  }
}

// 获取配置
async function handleGetConfig(sendResponse) {
  try {
    const config = await chrome.storage.sync.get();
    sendResponse({ success: true, config: config });
  } catch (error) {
    sendResponse({ error: error.message });
  }
}

// 保存配置
async function handleSaveConfig(config, sendResponse) {
  try {
    await chrome.storage.sync.set(config);
    sendResponse({ success: true });
  } catch (error) {
    sendResponse({ error: error.message });
  }
}

// 调用AI API
async function callAIAPI(apiConfig, prompt) {
  const maxRetries = 3;
  let lastError;

  for (let i = 0; i < maxRetries; i++) {
    try {
      // 确保API密钥只包含ASCII字符
      const cleanApiKey = apiConfig.apiKey.replace(/[^\x00-\x7F]/g, "");

      const requestBody = {
        model: apiConfig.model || 'qwen-plus',
        messages: [
          {
            role: 'system',
            content: '你是一个专业的内容总结助手，能够准确提取和总结各种类型文档的核心信息。请用中文回答。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: apiConfig.temperature || 0.7,
        max_tokens: apiConfig.maxTokens || 2000,
        top_p: 0.8
      };

      const response = await fetch(`${apiConfig.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${cleanApiKey}`
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;

        try {
          const errorData = JSON.parse(errorText);
          if (errorData.error && errorData.error.message) {
            errorMessage = errorData.error.message;
          } else if (errorData.message) {
            errorMessage = errorData.message;
          }
        } catch (e) {
          if (errorText) {
            errorMessage += ` - ${errorText}`;
          }
        }

        throw new Error(errorMessage);
      }

      const data = await response.json();

      // 处理不同的响应格式
      if (data.choices && data.choices.length > 0) {
        return data.choices[0].message.content;
      } else if (data.output && data.output.choices && data.output.choices.length > 0) {
        return data.output.choices[0].message.content;
      } else {
        throw new Error('API响应格式错误');
      }

    } catch (error) {
      lastError = error;
      console.error(`API调用失败 (尝试 ${i + 1}/${maxRetries}):`, error);

      // 如果不是最后一次尝试，等待后重试
      if (i < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
      }
    }
  }

  throw lastError;
}

// 测试API连接
async function handleTestConnection(apiConfig, sendResponse) {
  try {
    const testPrompt = '请回复"连接测试成功"';
    const response = await callAIAPI(apiConfig, testPrompt);

    if (response && response.includes('连接测试成功')) {
      sendResponse({ success: true, message: 'API连接测试成功' });
    } else {
      sendResponse({ success: true, message: 'API连接正常，但响应内容异常', response });
    }
  } catch (error) {
    console.error('API连接测试失败:', error);
    sendResponse({ success: false, message: formatErrorMessage(error) });
  }
}

// 导出配置
async function handleExportConfig(sendResponse) {
  try {
    const config = await chrome.storage.sync.get();
    const exportData = {
      version: '1.0.0',
      exportTime: new Date().toISOString(),
      config: {
        promptTemplates: config.promptTemplates,
        selectedTemplate: config.selectedTemplate,
        uiSettings: config.uiSettings
        // 不导出API配置和历史记录（安全考虑）
      }
    };

    sendResponse({ success: true, data: exportData });
  } catch (error) {
    console.error('导出配置失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 导入配置
async function handleImportConfig(importData, sendResponse) {
  try {
    if (!importData || !importData.config) {
      throw new Error('导入数据格式错误');
    }

    const currentConfig = await chrome.storage.sync.get();
    const newConfig = {
      ...currentConfig,
      ...importData.config
    };

    await chrome.storage.sync.set(newConfig);
    sendResponse({ success: true });
  } catch (error) {
    console.error('导入配置失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 清空历史记录
async function handleClearHistory(sendResponse) {
  try {
    await chrome.storage.sync.set({ history: [] });
    sendResponse({ success: true });
  } catch (error) {
    console.error('清空历史记录失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 重置所有设置
async function handleResetSettings(sendResponse) {
  try {
    await chrome.storage.sync.clear();

    // 重新设置默认配置
    const defaultConfig = {
      apiConfig: {
        provider: 'qwen',
        apiKey: HARDCODED_API_CONFIG.apiKey,
        baseUrl: HARDCODED_API_CONFIG.baseUrl,
        model: 'qwen-plus'
      },
      promptTemplates: {
        default: {
          name: '默认总结',
          prompt: '请对以下内容进行总结，提取关键信息和要点：\n\n{content}'
        },
        news: {
          name: '新闻总结',
          prompt: '请总结这篇新闻的主要内容，包括：\n1. 核心事件\n2. 关键人物\n3. 时间地点\n4. 影响和意义\n\n内容：{content}'
        },
        academic: {
          name: '学术文章',
          prompt: '请总结这篇学术文章，包括：\n1. 研究目的和问题\n2. 主要方法\n3. 核心发现\n4. 结论和意义\n\n内容：{content}'
        },
        technical: {
          name: '技术文档',
          prompt: '请总结这份技术文档的要点：\n1. 主要功能特性\n2. 使用方法\n3. 注意事项\n4. 适用场景\n\n内容：{content}'
        }
      },
      selectedTemplate: 'default',
      uiSettings: {
        theme: 'dark',
        sidebarWidth: 380,
        autoSummarize: false
      },
      history: []
    };

    await chrome.storage.sync.set(defaultConfig);
    sendResponse({ success: true });
  } catch (error) {
    console.error('重置设置失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 添加历史记录
async function handleAddHistoryRecord(record, sendResponse) {
  try {
    const { history = [] } = await chrome.storage.sync.get(['history']);

    // 添加新记录到历史记录开头
    history.unshift(record);

    // 限制历史记录数量（最多保存100条）
    if (history.length > 100) {
      history.splice(100);
    }

    await chrome.storage.sync.set({ history });
    sendResponse({ success: true });
  } catch (error) {
    console.error('添加历史记录失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 格式化错误信息
function formatErrorMessage(error) {
  const message = error.message || '未知错误';

  if (message.includes('401')) {
    return 'API密钥无效，请检查配置';
  } else if (message.includes('403')) {
    return 'API访问被拒绝，请检查权限';
  } else if (message.includes('429')) {
    return 'API调用频率超限，请稍后重试';
  } else if (message.includes('500')) {
    return 'API服务器错误，请稍后重试';
  } else if (message.includes('network') || message.includes('fetch')) {
    return '网络连接错误，请检查网络';
  } else {
    return message;
  }
}

// 提取内容为Markdown格式
async function extractToMarkdown(apiConfig, content, title, url, chunkIndex = null, totalChunks = null) {
  const chunkInfo = chunkIndex ? `（第${chunkIndex}/${totalChunks}部分）` : '';

  // 清理内容，确保不包含可能导致编码问题的字符
  const cleanContent = content.replace(/[\u0000-\u001F\u007F-\u009F]/g, '');
  const cleanTitle = title.replace(/[\u0000-\u001F\u007F-\u009F]/g, '');

  const prompt = `请将以下网页内容转换为标准的Markdown格式。要求：

1. 保持原文的结构层次，使用合适的标题级别（# ## ### 等）
2. 保留重要的文本格式（粗体、斜体、链接等）
3. 将列表转换为Markdown列表格式
4. 保留代码块和引用块的格式
5. 移除广告、导航菜单等无关内容
6. 确保输出的Markdown格式规范且易读
7. 如果有图片，保留图片的alt文本信息

网页标题：${cleanTitle}${chunkInfo}
网页链接：${url}

网页内容：
${cleanContent}

请直接输出转换后的Markdown内容，不要添加额外的说明文字：`;

  return await callAIAPI(apiConfig, prompt);
}

// 将内容分割成块
function splitContentIntoChunks(content, maxChunkSize) {
  const chunks = [];
  const paragraphs = content.split('\n\n');
  let currentChunk = '';

  for (const paragraph of paragraphs) {
    // 如果单个段落就超过限制，需要进一步分割
    if (paragraph.length > maxChunkSize) {
      if (currentChunk) {
        chunks.push(currentChunk.trim());
        currentChunk = '';
      }

      // 按句子分割长段落
      const sentences = paragraph.split(/[。！？.!?]/);
      let currentSentenceGroup = '';

      for (const sentence of sentences) {
        if (currentSentenceGroup.length + sentence.length > maxChunkSize) {
          if (currentSentenceGroup) {
            chunks.push(currentSentenceGroup.trim());
          }
          currentSentenceGroup = sentence;
        } else {
          currentSentenceGroup += sentence;
        }
      }

      if (currentSentenceGroup) {
        currentChunk = currentSentenceGroup;
      }
    } else {
      // 检查添加这个段落是否会超过限制
      if (currentChunk.length + paragraph.length > maxChunkSize) {
        if (currentChunk) {
          chunks.push(currentChunk.trim());
        }
        currentChunk = paragraph;
      } else {
        currentChunk += (currentChunk ? '\n\n' : '') + paragraph;
      }
    }
  }

  if (currentChunk) {
    chunks.push(currentChunk.trim());
  }

  return chunks;
}

// 合并Markdown块
function combineMarkdownChunks(markdownChunks, title, url) {
  const header = `# ${title}

> 来源：${url}
> 生成时间：${new Date().toLocaleString('zh-CN')}

---

`;

  const combinedContent = markdownChunks.join('\n\n---\n\n');

  return header + combinedContent;
}

// 页面内容提取函数（将在页面上下文中执行）
function extractPageContent() {
  // 移除不需要的元素
  const elementsToRemove = [
    'script', 'style', 'nav', 'header', 'footer', 
    'aside', '.advertisement', '.ads', '.sidebar',
    '.menu', '.navigation', '.breadcrumb', '.social-share',
    '.comments', '.related-posts', '.popup', '.modal'
  ];
  
  elementsToRemove.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    elements.forEach(el => el.remove());
  });
  
  // 尝试找到主要内容区域
  const contentSelectors = [
    'article', 'main', '.content', '.post-content', 
    '.article-content', '.entry-content', '.post-body',
    '.story-body', '.article-body', '#content'
  ];
  
  let mainContent = null;
  for (const selector of contentSelectors) {
    mainContent = document.querySelector(selector);
    if (mainContent) break;
  }
  
  // 如果没找到主要内容区域，使用body
  if (!mainContent) {
    mainContent = document.body;
  }
  
  // 提取文本内容
  let textContent = mainContent.innerText || mainContent.textContent || '';
  
  // 清理文本
  textContent = textContent
    .replace(/\s+/g, ' ')  // 合并多个空白字符
    .replace(/\n\s*\n/g, '\n')  // 合并多个换行
    .trim();
  
  // 限制内容长度（避免超过API限制）
  if (textContent.length > 8000) {
    textContent = textContent.substring(0, 8000) + '...';
  }
  
  return {
    content: textContent,
    title: document.title,
    url: window.location.href,
    wordCount: textContent.split(/\s+/).length
  };
}

// ==================== 钉钉认证相关处理函数 ====================

// 确保钉钉认证管理器已初始化
function ensureDingTalkAuthManager() {
  if (!dingTalkAuthManager) {
    dingTalkAuthManager = new DingTalkAuthManager();
    dingTalkErrorHandler = new DingTalkErrorHandler();
  }
  return dingTalkAuthManager;
}

// 获取钉钉认证状态
async function handleGetDingTalkAuthStatus(sendResponse) {
  try {
    const authManager = ensureDingTalkAuthManager();
    const authStatus = authManager.getAuthStatus();

    sendResponse({
      success: true,
      data: authStatus
    });

  } catch (error) {
    console.error('获取钉钉认证状态失败:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

// 发起钉钉登录
async function handleInitiateDingTalkLogin(sendResponse) {
  try {
    const authManager = ensureDingTalkAuthManager();
    const result = await authManager.initiateLogin();

    sendResponse({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('发起钉钉登录失败:', error);

    // 使用错误处理器处理错误
    if (dingTalkErrorHandler) {
      const errorResult = await dingTalkErrorHandler.handleAuthError(error, {
        traceId: `login_${Date.now()}`
      });

      sendResponse({
        success: false,
        error: error.message,
        errorInfo: errorResult.errorInfo
      });
    } else {
      sendResponse({
        success: false,
        error: error.message
      });
    }
  }
}

// 选择钉钉组织
async function handleSelectDingTalkOrg(corpId, sendResponse) {
  try {
    const authManager = ensureDingTalkAuthManager();
    const selectedOrg = await authManager.selectOrganization(corpId);

    sendResponse({
      success: true,
      data: selectedOrg
    });

  } catch (error) {
    console.error('选择钉钉组织失败:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

// 钉钉登出
async function handleDingTalkLogout(sendResponse) {
  try {
    const authManager = ensureDingTalkAuthManager();
    const result = await authManager.logout();

    sendResponse({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('钉钉登出失败:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

// 刷新钉钉认证状态
async function handleRefreshDingTalkAuth(sendResponse) {
  try {
    const authManager = ensureDingTalkAuthManager();

    // 重新验证Cookie并获取用户信息
    const cookieValid = await authManager.validateAuthCookie();
    if (cookieValid) {
      await authManager.fetchUserInfoAndSync();
    } else {
      await authManager.clearAuthState();
    }

    const authStatus = authManager.getAuthStatus();

    sendResponse({
      success: true,
      data: authStatus
    });

  } catch (error) {
    console.error('刷新钉钉认证状态失败:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

// 验证钉钉Cookie（调试用）
async function handleValidateDingTalkCookies(sendResponse) {
  try {
    const authManager = ensureDingTalkAuthManager();
    const isValid = await authManager.validateAuthCookie();

    // 获取详细的Cookie信息
    const cookies = await chrome.cookies.getAll({ domain: '.dingtalk.com' });
    const authCookieNames = [
      'account', 'login_aliyunid_ticket', '_tb_token_',
      'sessionid', 'sid', 'token', 'auth', 'login', 'user', 'session',
      'dingtalk_session', 'dt_session', 'dt_token', 'dt_auth'
    ];

    const authCookies = cookies.filter(c =>
      (authCookieNames.includes(c.name) ||
       c.name.includes('login') ||
       c.name.includes('auth') ||
       c.name.includes('session') ||
       c.name.includes('token')) && c.value
    );

    sendResponse({
      success: true,
      data: {
        isValid: isValid,
        totalCookies: cookies.length,
        authCookies: authCookies.length,
        cookieDetails: authCookies.map(c => ({
          name: c.name,
          hasValue: !!c.value,
          valueLength: c.value ? c.value.length : 0,
          domain: c.domain,
          expirationDate: c.expirationDate,
          isExpired: c.expirationDate && c.expirationDate * 1000 < Date.now()
        }))
      }
    });

  } catch (error) {
    console.error('验证钉钉Cookie失败:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

// 强制检查钉钉认证状态
async function handleForceDingTalkAuthCheck(sendResponse) {
  try {
    const authManager = ensureDingTalkAuthManager();
    const traceId = `force_check_${Date.now()}`;

    console.log(`[${traceId}] 强制检查钉钉认证状态...`);

    // 检查Cookie状态
    const cookieValid = await authManager.validateAuthCookie();
    const currentAuthState = authManager.isAuthenticated;

    console.log(`[${traceId}] Cookie有效性: ${cookieValid}, 当前认证状态: ${currentAuthState}`);

    let actionTaken = 'none';

    // 如果Cookie有效但本地状态显示未认证，触发登录处理
    if (cookieValid && !currentAuthState) {
      console.log(`[${traceId}] 检测到Cookie有效但本地状态未认证，触发登录处理`);
      await authManager.handleUserLogin(traceId);
      actionTaken = 'triggered_login';
    }
    // 如果Cookie无效但本地状态显示已认证，触发登出处理
    else if (!cookieValid && currentAuthState) {
      console.log(`[${traceId}] 检测到Cookie无效但本地状态已认证，触发登出处理`);
      await authManager.handleUserLogout(traceId);
      actionTaken = 'triggered_logout';
    }

    // 获取最新的认证状态
    const authStatus = authManager.getAuthStatus();

    sendResponse({
      success: true,
      data: {
        authStatus: authStatus,
        cookieValid: cookieValid,
        actionTaken: actionTaken,
        traceId: traceId
      }
    });

  } catch (error) {
    console.error('强制检查钉钉认证状态失败:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

// 获取钉钉认证调试信息
async function handleGetDingTalkDebugInfo(sendResponse) {
  try {
    const authManager = ensureDingTalkAuthManager();
    const debugInfo = await authManager.getDebugInfo();

    sendResponse({
      success: true,
      data: debugInfo
    });

  } catch (error) {
    console.error('获取钉钉认证调试信息失败:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}
