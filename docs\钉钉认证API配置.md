# 钉钉文档闪存API端点配置

## 基础配置

### 环境域名
```javascript
const API_BASE_URLS = {
    production: "https://docs.dingtalk.com",
    pre: "https://pre-docs.dingtalk.com"
};

// 获取当前环境API基础URL
function getApiBaseUrl() {
    const env = process.env.BUILD_ENV === 'pre' ? 'pre' : 'production';
    return API_BASE_URLS[env];
}
```

## 认证相关API

### 1. 登录页面
- **URL**: `${baseUrl}/i`
- **方法**: GET (页面跳转)
- **用途**: 用户登录认证页面
- **示例**: 
  - 生产: `https://docs.dingtalk.com/i`
  - 预发: `https://pre-docs.dingtalk.com/i`

### 2. 用户组织信息API
- **端点**: `/portal/api/v1/mine/orgs`
- **方法**: GET
- **参数**: 
  - `orgTypes`: 组织类型参数
- **完整URL**: `${baseUrl}/portal/api/v1/mine/orgs?orgTypes=${types}`
- **用途**: 获取用户所属的组织列表
- **认证**: 需要Cookie认证

### 3. 用户设置API
- **端点**: `/openapi/api/user/settings`
- **方法**: GET
- **完整URL**: `${baseUrl}/openapi/api/user/settings`
- **用途**: 获取用户个人设置
- **认证**: 需要Cookie认证

## 文档操作API

### 4. 文档保存API (推测)
- **端点**: `/portal/api/v1/docs/save`
- **方法**: POST
- **用途**: 保存页面内容到钉钉文档
- **认证**: 需要Cookie认证

### 5. 文档上传API (推测)
- **端点**: `/portal/api/v1/docs/upload`
- **方法**: POST
- **用途**: 上传文档内容
- **认证**: 需要Cookie认证

## 语雀集成API

### 6. 语雀文档导入 (推测)
- **端点**: `/openapi/api/lakebook/import`
- **方法**: POST
- **用途**: 从语雀导入文档
- **认证**: 需要Cookie认证

## API调用模式

### 标准请求头
```javascript
const defaultHeaders = {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest'
};
```

### 错误处理
```javascript
async function apiCall(url, options = {}) {
    try {
        const response = await fetch(url, {
            ...options,
            headers: {
                ...defaultHeaders,
                ...options.headers
            },
            credentials: 'include' // 包含Cookie
        });
        
        if (!response.ok) {
            throw new Error(`API调用失败: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error('API调用错误:', error);
        throw error;
    }
}
```

### 认证状态检查
```javascript
async function checkAuthStatus() {
    try {
        const response = await apiCall(`${getApiBaseUrl()}/openapi/api/user/settings`);
        return response.success;
    } catch (error) {
        return false;
    }
}
```

## 企业权限API

### 7. 企业权限验证
- **端点**: `/portal/api/v1/corp/permissions`
- **方法**: GET
- **参数**: 
  - `corpId`: 企业ID
- **用途**: 验证用户在特定企业的权限

### 8. 企业配置获取
- **端点**: `/portal/api/v1/corp/config`
- **方法**: GET
- **参数**: 
  - `corpId`: 企业ID
- **用途**: 获取企业相关配置

## Webhook配置API

### 9. Webhook设置
- **端点**: `/openapi/api/webhook/config`
- **方法**: POST/GET
- **用途**: 配置自定义Webhook
- **认证**: 需要Cookie认证

## 内容处理API

### 10. 页面内容解析
- **端点**: `/portal/api/v1/content/parse`
- **方法**: POST
- **用途**: 解析网页内容
- **请求体**: 
```json
{
    "url": "页面URL",
    "content": "页面HTML内容",
    "title": "页面标题"
}
```

### 11. 内容格式转换
- **端点**: `/portal/api/v1/content/convert`
- **方法**: POST
- **用途**: 转换内容格式
- **支持格式**: HTML, Markdown, 富文本

## 错误码定义

### 认证相关错误
- `401`: 未认证
- `403`: 权限不足
- `404`: 资源不存在
- `429`: 请求频率限制

### 业务错误码
- `10001`: 用户未登录
- `10002`: 企业权限不足
- `10003`: 文档保存失败
- `10004`: 内容格式错误

## 请求限制

### 频率限制
- **用户API**: 每分钟100次请求
- **企业API**: 每分钟500次请求
- **内容API**: 每分钟50次请求

### 内容大小限制
- **单次上传**: 最大10MB
- **文档内容**: 最大5MB
- **图片附件**: 最大2MB

## 监控和日志

### API调用追踪
```javascript
function generateTraceId() {
    return `trace_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

async function trackedApiCall(url, options = {}) {
    const traceId = generateTraceId();
    console.log(`[${traceId}] API调用开始:`, url);
    
    try {
        const result = await apiCall(url, options);
        console.log(`[${traceId}] API调用成功`);
        return result;
    } catch (error) {
        console.error(`[${traceId}] API调用失败:`, error);
        throw error;
    }
}
```

### 性能监控
- 记录API响应时间
- 监控错误率
- 追踪用户操作路径

## 开发调试

### API测试工具
```javascript
// 在Chrome DevTools Console中测试API
async function testApi(endpoint, options = {}) {
    const baseUrl = 'https://docs.dingtalk.com'; // 或预发环境
    const url = `${baseUrl}${endpoint}`;
    
    try {
        const response = await fetch(url, {
            credentials: 'include',
            ...options
        });
        console.log('响应状态:', response.status);
        console.log('响应数据:', await response.json());
    } catch (error) {
        console.error('测试失败:', error);
    }
}
```

### 常用测试命令
```javascript
// 测试用户组织信息
testApi('/portal/api/v1/mine/orgs?orgTypes=1,2,3');

// 测试用户设置
testApi('/openapi/api/user/settings');

// 测试认证状态
testApi('/openapi/api/user/profile');
```