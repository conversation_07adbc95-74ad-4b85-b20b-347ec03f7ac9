<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钉钉认证安全性测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #333;
        }
        
        .test-button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 8px;
            margin-bottom: 8px;
        }
        
        .test-button:hover {
            background: #40a9ff;
        }
        
        .test-button.danger {
            background: #ff4d4f;
        }
        
        .test-button.danger:hover {
            background: #ff7875;
        }
        
        .test-result {
            background: #f6f8fa;
            border: 1px solid #e1e4e8;
            border-radius: 4px;
            padding: 12px;
            margin-top: 12px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .security-score {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: 600;
            color: white;
        }
        
        .score-high { background: #52c41a; }
        .score-medium { background: #faad14; }
        .score-low { background: #ff4d4f; }
        
        .security-event {
            background: #fff2e8;
            border-left: 4px solid #fa8c16;
            padding: 8px 12px;
            margin: 8px 0;
            border-radius: 0 4px 4px 0;
        }
        
        .security-event.error {
            background: #fff2f0;
            border-left-color: #ff4d4f;
        }
        
        .security-event.warning {
            background: #fffbe6;
            border-left-color: #faad14;
        }
        
        .security-event.info {
            background: #e6f7ff;
            border-left-color: #1890ff;
        }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <h1>钉钉认证安全性测试</h1>
    
    <div class="grid">
        <!-- Cookie安全性测试 -->
        <div class="test-container">
            <div class="test-title">Cookie安全性测试</div>
            <button class="test-button" onclick="testCookieSecurity()">检查Cookie安全性</button>
            <button class="test-button" onclick="testCookieIntegrity()">验证Cookie完整性</button>
            <div id="cookieSecurityResult" class="test-result" style="display: none;"></div>
        </div>
        
        <!-- API安全性测试 -->
        <div class="test-container">
            <div class="test-title">API安全性测试</div>
            <button class="test-button" onclick="testApiSecurity()">测试API安全性</button>
            <button class="test-button danger" onclick="testMaliciousUrl()">测试恶意URL</button>
            <div id="apiSecurityResult" class="test-result" style="display: none;"></div>
        </div>
        
        <!-- 频率限制测试 -->
        <div class="test-container">
            <div class="test-title">频率限制测试</div>
            <button class="test-button" onclick="testRateLimit()">测试请求频率限制</button>
            <button class="test-button" onclick="clearRateLimit()">清除频率限制</button>
            <div id="rateLimitResult" class="test-result" style="display: none;"></div>
        </div>
        
        <!-- 登录安全测试 -->
        <div class="test-container">
            <div class="test-title">登录安全测试</div>
            <button class="test-button" onclick="testLoginSecurity()">测试登录安全</button>
            <button class="test-button danger" onclick="simulateFailedLogin()">模拟登录失败</button>
            <div id="loginSecurityResult" class="test-result" style="display: none;"></div>
        </div>
    </div>
    
    <!-- 安全报告 -->
    <div class="test-container">
        <div class="test-title">安全报告</div>
        <button class="test-button" onclick="generateSecurityReport()">生成安全报告</button>
        <button class="test-button" onclick="clearSecurityEvents()">清除安全事件</button>
        <div id="securityReport" class="test-result" style="display: none;"></div>
    </div>
    
    <!-- 实时安全事件 -->
    <div class="test-container">
        <div class="test-title">实时安全事件</div>
        <button class="test-button" onclick="clearSecurityLog()">清空日志</button>
        <div id="securityLog">等待安全事件...</div>
    </div>

    <script src="../utils/dingtalk-security.js"></script>
    <script>
        let securityManager = new DingTalkSecurityManager();
        
        // 记录安全事件到UI
        function logSecurityEvent(message, type = 'info') {
            const logContainer = document.getElementById('securityLog');
            const timestamp = new Date().toLocaleTimeString();
            
            const eventDiv = document.createElement('div');
            eventDiv.className = `security-event ${type}`;
            eventDiv.innerHTML = `
                <strong>[${timestamp}]</strong> ${message}
            `;
            
            logContainer.appendChild(eventDiv);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        function clearSecurityLog() {
            document.getElementById('securityLog').innerHTML = '安全日志已清空';
        }
        
        // 显示测试结果
        function showResult(elementId, data, success = true) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.textContent = JSON.stringify(data, null, 2);
            element.style.color = success ? '#52c41a' : '#ff4d4f';
        }
        
        // 测试Cookie安全性
        async function testCookieSecurity() {
            logSecurityEvent('开始Cookie安全性测试', 'info');
            
            try {
                const cookies = await chrome.cookies.getAll({
                    domain: '.dingtalk.com'
                });
                
                const results = [];
                
                for (const cookie of cookies) {
                    const validation = securityManager.validateCookieSecurity(cookie);
                    results.push({
                        name: cookie.name,
                        domain: cookie.domain,
                        isValid: validation.isValid,
                        issues: validation.issues
                    });
                    
                    if (!validation.isValid) {
                        logSecurityEvent(`Cookie安全检查失败: ${cookie.name}`, 'error');
                        validation.issues.forEach(issue => {
                            logSecurityEvent(`- ${issue.message}`, issue.level);
                        });
                    }
                }
                
                showResult('cookieSecurityResult', results, true);
                logSecurityEvent(`Cookie安全性测试完成，检查了 ${cookies.length} 个Cookie`, 'info');
                
            } catch (error) {
                logSecurityEvent(`Cookie安全性测试失败: ${error.message}`, 'error');
                showResult('cookieSecurityResult', { error: error.message }, false);
            }
        }
        
        // 测试Cookie完整性
        async function testCookieIntegrity() {
            logSecurityEvent('开始Cookie完整性测试', 'info');
            
            try {
                // 模拟认证管理器的Cookie验证
                const cookies = await chrome.cookies.getAll({
                    domain: '.dingtalk.com',
                    name: 'account'
                });
                
                if (cookies.length === 0) {
                    logSecurityEvent('未找到钉钉认证Cookie', 'warning');
                    showResult('cookieSecurityResult', { message: '未找到认证Cookie' }, false);
                    return;
                }
                
                const authCookie = cookies[0];
                const validation = securityManager.validateCookieSecurity(authCookie);
                
                const integrityResult = {
                    cookieFound: true,
                    isValid: validation.isValid,
                    issues: validation.issues,
                    cookieDetails: {
                        domain: authCookie.domain,
                        path: authCookie.path,
                        secure: authCookie.secure,
                        httpOnly: authCookie.httpOnly,
                        hasValue: !!authCookie.value,
                        expirationDate: authCookie.expirationDate
                    }
                };
                
                showResult('cookieSecurityResult', integrityResult, validation.isValid);
                
                if (validation.isValid) {
                    logSecurityEvent('Cookie完整性验证通过', 'info');
                } else {
                    logSecurityEvent('Cookie完整性验证失败', 'error');
                }
                
            } catch (error) {
                logSecurityEvent(`Cookie完整性测试失败: ${error.message}`, 'error');
                showResult('cookieSecurityResult', { error: error.message }, false);
            }
        }
        
        // 测试API安全性
        function testApiSecurity() {
            logSecurityEvent('开始API安全性测试', 'info');
            
            const testUrls = [
                'https://docs.dingtalk.com/portal/api/v1/mine/orgs',
                'https://pre-docs.dingtalk.com/openapi/api/user/settings',
                'https://oapi.dingtalk.com/robot/send',
                'https://api.dingtalk.com/v1.0/oauth2/userAccessToken'
            ];
            
            const results = [];
            
            testUrls.forEach(url => {
                const validation = securityManager.validateApiSecurity(url);
                results.push({
                    url: url,
                    isValid: validation.isValid,
                    issues: validation.issues
                });
                
                if (validation.isValid) {
                    logSecurityEvent(`API安全检查通过: ${url}`, 'info');
                } else {
                    logSecurityEvent(`API安全检查失败: ${url}`, 'error');
                    validation.issues.forEach(issue => {
                        logSecurityEvent(`- ${issue.message}`, issue.level);
                    });
                }
            });
            
            showResult('apiSecurityResult', results, true);
        }
        
        // 测试恶意URL
        function testMaliciousUrl() {
            logSecurityEvent('开始恶意URL测试', 'warning');
            
            const maliciousUrls = [
                'http://docs.dingtalk.com/api/test', // HTTP instead of HTTPS
                'https://evil.dingtalk.com/api/test', // Invalid subdomain
                'https://docs.dingtalk.com/../../../etc/passwd', // Path traversal
                'https://docs.dingtalk.com/api/<script>alert(1)</script>', // XSS attempt
                'https://malicious.com/api/test' // Completely different domain
            ];
            
            const results = [];
            
            maliciousUrls.forEach(url => {
                const validation = securityManager.validateApiSecurity(url);
                results.push({
                    url: url,
                    isValid: validation.isValid,
                    issues: validation.issues
                });
                
                if (!validation.isValid) {
                    logSecurityEvent(`恶意URL被阻止: ${url}`, 'info');
                    securityManager.recordSecurityEvent('suspicious_request', { url: url });
                } else {
                    logSecurityEvent(`警告：恶意URL未被阻止: ${url}`, 'error');
                }
            });
            
            showResult('apiSecurityResult', results, true);
        }
        
        // 测试频率限制
        function testRateLimit() {
            logSecurityEvent('开始频率限制测试', 'info');
            
            const hostname = 'docs.dingtalk.com';
            const results = [];
            
            // 快速发送多个请求
            for (let i = 0; i < 70; i++) {
                const rateLimitResult = securityManager.checkRateLimit(hostname);
                results.push({
                    request: i + 1,
                    allowed: rateLimitResult.allowed,
                    remainingRequests: rateLimitResult.remainingRequests,
                    remainingTime: rateLimitResult.remainingTime
                });
                
                if (!rateLimitResult.allowed) {
                    logSecurityEvent(`请求被频率限制阻止 (第${i + 1}个请求)`, 'warning');
                    securityManager.recordSecurityEvent('rate_limit_exceeded', { 
                        hostname: hostname,
                        requestNumber: i + 1 
                    });
                    break;
                }
            }
            
            showResult('rateLimitResult', results, true);
            logSecurityEvent(`频率限制测试完成，发送了 ${results.length} 个请求`, 'info');
        }
        
        // 清除频率限制
        function clearRateLimit() {
            securityManager.cleanup();
            logSecurityEvent('频率限制已清除', 'info');
            showResult('rateLimitResult', { message: '频率限制已清除' }, true);
        }
        
        // 测试登录安全
        function testLoginSecurity() {
            logSecurityEvent('开始登录安全测试', 'info');
            
            const userAgent = navigator.userAgent;
            const ipAddress = '127.0.0.1'; // 模拟IP
            
            // 检查当前锁定状态
            const lockStatus = securityManager.isLoginLocked(userAgent, ipAddress);
            
            const result = {
                currentStatus: lockStatus,
                testResults: []
            };
            
            if (lockStatus.isLocked) {
                logSecurityEvent(`账户已被锁定，剩余时间: ${Math.round(lockStatus.remainingTime / 1000)}秒`, 'warning');
            } else {
                logSecurityEvent('账户未被锁定，可以进行登录测试', 'info');
                
                // 模拟成功登录
                const successResult = securityManager.recordLoginAttempt(true, userAgent, ipAddress);
                result.testResults.push({
                    type: 'success',
                    result: successResult
                });
                logSecurityEvent('模拟登录成功', 'info');
            }
            
            showResult('loginSecurityResult', result, true);
        }
        
        // 模拟登录失败
        function simulateFailedLogin() {
            logSecurityEvent('模拟登录失败', 'warning');
            
            const userAgent = navigator.userAgent;
            const ipAddress = '127.0.0.1'; // 模拟IP
            
            const result = securityManager.recordLoginAttempt(false, userAgent, ipAddress);
            
            logSecurityEvent(`登录失败记录，剩余尝试次数: ${result.remainingAttempts}`, 'warning');
            
            if (result.isLocked) {
                logSecurityEvent(`账户已被锁定，锁定到: ${new Date(result.lockExpiry).toLocaleString()}`, 'error');
                securityManager.recordSecurityEvent('login_lockout', {
                    userAgent: userAgent,
                    ipAddress: ipAddress,
                    lockExpiry: result.lockExpiry
                });
            }
            
            showResult('loginSecurityResult', result, !result.isLocked);
        }
        
        // 生成安全报告
        function generateSecurityReport() {
            logSecurityEvent('生成安全报告', 'info');
            
            const report = securityManager.getSecurityReport();
            
            // 添加安全评分样式
            let scoreClass = 'score-high';
            if (report.securityScore < 70) scoreClass = 'score-medium';
            if (report.securityScore < 40) scoreClass = 'score-low';
            
            const reportHtml = `
                <div>
                    <h3>安全评分: <span class="security-score ${scoreClass}">${report.securityScore}/100</span></h3>
                    <pre>${JSON.stringify(report, null, 2)}</pre>
                </div>
            `;
            
            document.getElementById('securityReport').innerHTML = reportHtml;
            document.getElementById('securityReport').style.display = 'block';
            
            logSecurityEvent(`安全报告生成完成，评分: ${report.securityScore}/100`, 'info');
        }
        
        // 清除安全事件
        function clearSecurityEvents() {
            securityManager.securityEvents = [];
            securityManager.cleanup();
            logSecurityEvent('所有安全事件已清除', 'info');
            showResult('securityReport', { message: '安全事件已清除' }, true);
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            logSecurityEvent('钉钉认证安全性测试页面已加载', 'info');
            
            // 定期清理过期数据
            setInterval(() => {
                securityManager.cleanup();
            }, 60000); // 每分钟清理一次
        });
    </script>
</body>
</html>
